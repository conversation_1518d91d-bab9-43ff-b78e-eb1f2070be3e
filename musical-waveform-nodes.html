<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Musical Waveform - 2D Node Plane</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
            perspective: 1000px;
        }

        .waveform-plane {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotateX(60deg);
            transform-style: preserve-3d;
            width: 800px;
            height: 600px;
        }

        .node {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #00ffff;
            border-radius: 50%;
            box-shadow: 0 0 4px rgba(0, 255, 255, 0.6);
            transform-style: preserve-3d;
            will-change: transform, background-color;
        }

        .node.active {
            background: #ff00ff;
            box-shadow: 0 0 8px rgba(255, 0, 255, 0.8);
            transform: scale(1.8) translateZ(15px);
        }

        .node.medium {
            background: #ffff00;
            box-shadow: 0 0 6px rgba(255, 255, 0, 0.7);
            transform: scale(1.4) translateZ(8px);
        }

        .controls {
            position: absolute;
            top: 30px;
            left: 30px;
            color: #ffffff;
            z-index: 1000;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .audio-controls {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            align-items: center;
            z-index: 1000;
        }

        button {
            padding: 12px 24px;
            background: rgba(0, 255, 255, 0.2);
            border: 2px solid rgba(0, 255, 255, 0.5);
            color: #00ffff;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        button:hover {
            background: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
        }

        button:active {
            transform: scale(0.95);
        }

        .frequency-display {
            color: #00ffff;
            font-size: 14px;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }

        .info {
            position: absolute;
            top: 30px;
            right: 30px;
            color: #ffffff;
            text-align: right;
            font-size: 12px;
            opacity: 0.7;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="controls">
            <h2>Musical Waveform Visualizer</h2>
            <p>2D Node Plane Audio Reactive</p>
        </div>

        <div class="info">
            <p>Upload MP3/audio file or use microphone</p>
            <p>Nodes react to frequency and amplitude</p>
            <p>Higher frequencies = brighter colors</p>
        </div>

        <div class="waveform-plane" id="waveform"></div>

        <div class="audio-controls">
            <button id="startBtn">Start Microphone</button>
            <button id="stopBtn" disabled>Stop Audio</button>
            <input type="file" id="fileInput" accept="audio/*" style="display: none;">
            <button id="uploadBtn">Upload MP3/Audio</button>
            <div class="frequency-display" id="freqDisplay">Frequency: 0 Hz</div>
        </div>
    </div>

    <script>
        const waveform = document.getElementById('waveform');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const uploadBtn = document.getElementById('uploadBtn');
        const fileInput = document.getElementById('fileInput');
        const freqDisplay = document.getElementById('freqDisplay');

        let audioContext;
        let analyser;
        let microphone;
        let audioSource;
        let audioElement;
        let dataArray;
        let animationId;
        let nodes = [];
        let isPlaying = false;

        // Create 2D grid of nodes - Reduced for performance
        const gridWidth = 30;
        const gridHeight = 20;
        const nodeSpacing = 25;

        function createNodeGrid() {
            for (let x = 0; x < gridWidth; x++) {
                nodes[x] = [];
                for (let y = 0; y < gridHeight; y++) {
                    const node = document.createElement('div');
                    node.className = 'node';
                    
                    const posX = (x - gridWidth/2) * nodeSpacing;
                    const posY = (y - gridHeight/2) * nodeSpacing;
                    
                    node.style.left = `${400 + posX}px`;
                    node.style.top = `${300 + posY}px`;
                    
                    waveform.appendChild(node);
                    nodes[x][y] = {
                        element: node,
                        baseX: posX,
                        baseY: posY,
                        frequency: 0,
                        amplitude: 0
                    };
                }
            }
        }

        // Generate synthetic waveform with peaks and valleys - Optimized
        function generateSyntheticWave() {
            const time = Date.now() * 0.001;

            for (let x = 0; x < gridWidth; x++) {
                for (let y = 0; y < gridHeight; y++) {
                    const node = nodes[x][y];

                    // Create dramatic peaks and valleys
                    const wave1 = Math.sin((x * 0.4) + (time * 2)) * 0.8;
                    const wave2 = Math.sin((y * 0.3) + (time * 1.5)) * 0.6;
                    const ripple = Math.sin(Math.sqrt((x-15)*(x-15) + (y-10)*(y-10)) * 0.5 + time * 3) * 0.4;

                    // Combine waves for complex terrain
                    const combinedWave = wave1 + wave2 + ripple;
                    const height = combinedWave * 40; // Amplify for dramatic peaks/valleys
                    const intensity = Math.abs(combinedWave);

                    // Create peaks (positive) and valleys (negative)
                    let className = 'node';
                    let zOffset = height;

                    if (height > 25) { // High peaks
                        className = 'node active';
                        zOffset = height + 20; // Extra elevation for peaks
                    } else if (height > 10) { // Medium hills
                        className = 'node medium';
                        zOffset = height + 10;
                    } else if (height < -25) { // Deep valleys
                        className = 'node active';
                        zOffset = height - 20; // Extra depth for valleys
                    } else if (height < -10) { // Shallow valleys
                        className = 'node medium';
                        zOffset = height - 10;
                    }

                    // Only update if changed to reduce DOM manipulation
                    if (node.element.className !== className) {
                        node.element.className = className;
                    }
                    node.element.style.transform = `translateZ(${zOffset}px)`;
                }
            }
        }

        // Process real audio data - Optimized for performance
        function processAudioData() {
            analyser.getByteFrequencyData(dataArray);

            // Calculate dominant frequency (less frequently for performance)
            if (Math.random() < 0.1) { // Only update 10% of frames
                let maxAmplitude = 0;
                let dominantFreq = 0;

                for (let i = 0; i < dataArray.length; i += 4) { // Skip every 4th sample
                    if (dataArray[i] > maxAmplitude) {
                        maxAmplitude = dataArray[i];
                        dominantFreq = (i * audioContext.sampleRate) / (2 * dataArray.length);
                    }
                }
                freqDisplay.textContent = `Frequency: ${Math.round(dominantFreq)} Hz`;
            }

            // Map frequency data to node grid - Optimized
            for (let x = 0; x < gridWidth; x++) {
                const freqIndex = Math.floor((x / gridWidth) * dataArray.length);
                const amplitude = dataArray[freqIndex] / 255;

                for (let y = 0; y < gridHeight; y++) {
                    const node = nodes[x][y];

                    // Simplified wave calculation
                    const waveEffect = Math.sin((y / gridHeight) * Math.PI + (amplitude * 8)) * amplitude;
                    const intensity = amplitude + Math.abs(waveEffect) * 0.4;

                    // Batch DOM updates
                    let className = 'node';
                    if (intensity > 0.6) {
                        className = 'node active';
                    } else if (intensity > 0.25) {
                        className = 'node medium';
                    }

                    // Only update if changed
                    if (node.element.className !== className) {
                        node.element.className = className;
                    }

                    // Reduced 3D displacement for performance
                    node.element.style.transform = `translateZ(${waveEffect * 25}px)`;
                }
            }
        }

        function animate() {
            if (audioContext && audioContext.state === 'running' && (microphone || isPlaying)) {
                processAudioData();
            } else {
                generateSyntheticWave();
            }

            animationId = requestAnimationFrame(animate);
        }

        async function startAudio() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 256;
                dataArray = new Uint8Array(analyser.frequencyBinCount);
                
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                microphone = audioContext.createMediaStreamSource(stream);
                microphone.connect(analyser);
                
                startBtn.disabled = true;
                stopBtn.disabled = false;
                
                console.log('Audio started successfully');
            } catch (error) {
                console.log('Microphone access denied, using synthetic waves');
                freqDisplay.textContent = 'Using synthetic waveform';
            }
        }

        async function loadAudioFile(file) {
            try {
                // Stop any existing audio
                stopAudio();

                // Create new audio context if needed
                if (!audioContext) {
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }

                // Create audio element
                audioElement = new Audio();
                audioElement.src = URL.createObjectURL(file);
                audioElement.loop = true;

                // Set up audio analysis
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 256;
                dataArray = new Uint8Array(analyser.frequencyBinCount);

                // Connect audio to analyser
                audioSource = audioContext.createMediaElementSource(audioElement);
                audioSource.connect(analyser);
                analyser.connect(audioContext.destination);

                // Play the audio
                await audioElement.play();
                isPlaying = true;

                startBtn.disabled = true;
                stopBtn.disabled = false;
                freqDisplay.textContent = `Playing: ${file.name}`;

                console.log('Audio file loaded and playing');
            } catch (error) {
                console.error('Error loading audio file:', error);
                freqDisplay.textContent = 'Error loading file';
            }
        }

        function stopAudio() {
            if (audioElement) {
                audioElement.pause();
                audioElement.currentTime = 0;
                audioElement = null;
            }

            if (audioContext && audioContext.state !== 'closed') {
                audioContext.close();
                audioContext = null;
            }

            if (microphone) {
                microphone.disconnect();
                microphone = null;
            }

            isPlaying = false;
            startBtn.disabled = false;
            stopBtn.disabled = true;
            freqDisplay.textContent = 'Audio stopped';
        }

        // Event listeners
        startBtn.addEventListener('click', startAudio);
        stopBtn.addEventListener('click', stopAudio);
        uploadBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                loadAudioFile(file);
            }
        });

        // Initialize
        createNodeGrid();
        animate();

        console.log(`Created ${gridWidth * gridHeight} optimized nodes in waveform plane`);
    </script>
</body>
</html>
