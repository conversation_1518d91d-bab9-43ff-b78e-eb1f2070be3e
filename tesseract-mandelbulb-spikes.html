<!DOCTYPE html>
<html>
<head>
    <title>Tesseract Mandelbulb with Spikes - Audio Reactive</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: 'Orbitron', monospace;
            overflow: hidden;
            color: #00ffff;
        }
        
        canvas {
            display: block;
            cursor: crosshair;
        }
        
        .controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 20, 40, 0.9);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #00ffff;
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            max-width: 300px;
            z-index: 1000;
        }
        
        .slider-container {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .slider-container label {
            min-width: 120px;
            font-size: 12px;
            color: #00ffff;
        }
        
        input[type="range"] {
            flex: 1;
            height: 6px;
            background: linear-gradient(90deg, #001122, #00ffff);
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            background: #00ffff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }
        
        .audio-controls {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #00ffff;
        }
        
        button {
            background: linear-gradient(45deg, #001122, #003344);
            color: #00ffff;
            border: 2px solid #00ffff;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-size: 11px;
            margin: 5px;
            transition: all 0.3s;
        }
        
        button:hover {
            background: linear-gradient(45deg, #003344, #005566);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
        }
        
        .info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 20, 40, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ffff;
            font-size: 11px;
            max-width: 250px;
        }
    </style>
</head>
<body>
<div class="controls">
    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
        <span style="font-size: 24px;">🔷</span>
        <strong>TESSERACT SPIKES</strong>
    </div>
    
    <div class="slider-container">
        <label>🎵 Audio Reactivity</label>
        <input type="range" id="musicReactivity" min="0" max="100" value="75">
        <span id="reactivityValue">75</span>
    </div>
    
    <div class="slider-container">
        <label>⚡ Spike Intensity</label>
        <input type="range" id="spikeIntensity" min="0" max="100" value="60">
        <span id="spikeValue">60</span>
    </div>
    
    <div class="slider-container">
        <label>🔄 Transform Speed</label>
        <input type="range" id="transformSpeed" min="0" max="100" value="40">
        <span id="speedValue">40</span>
    </div>
    
    <div class="slider-container">
        <label>🌈 Color Shift</label>
        <input type="range" id="colorShift" min="0" max="100" value="50">
        <span id="colorValue">50</span>
    </div>
    
    <div class="slider-container">
        <label>✨ Glow Intensity</label>
        <input type="range" id="glowIntensity" min="0" max="100" value="70">
        <span id="glowValue">70</span>
    </div>
    
    <div class="audio-controls">
        <strong style="font-size: 12px;">🎤 AUDIO INPUT</strong><br>
        <button id="startAudio">START MICROPHONE</button>
        <button id="stopAudio" disabled>STOP AUDIO</button>
        <input type="file" id="audioFile" accept="audio/*" style="display: none;">
        <button onclick="document.getElementById('audioFile').click()">LOAD AUDIO FILE</button>
    </div>
</div>

<div class="info">
    <strong>🎮 CONTROLS</strong><br>
    <kbd>WASD</kbd> Move Camera<br>
    <kbd>Mouse</kbd> Look Around<br>
    <kbd>Q/E</kbd> Up/Down<br>
    <kbd>Space</kbd> Auto-Orbit<br>
    <br>
    <strong>🔷 TESSERACT SPIKES</strong><br>
    A 4D hypercube fractal with<br>
    dynamic spikes emerging from<br>
    each face, reacting to audio<br>
    frequencies in real-time.
</div>

<canvas id="canvas"></canvas>

<script>
// WebGL setup
const canvas = document.getElementById('canvas');
const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');

if (!gl) {
    alert('WebGL not supported');
}

// Audio setup
let audioContext, analyser, microphone, audioSource;
let bassLevel = 0, midLevel = 0, trebleLevel = 0;
let dataArray;

// Control variables
let musicReactivity = 0.75;
let spikeIntensity = 0.6;
let transformSpeed = 0.4;
let colorShift = 0.5;
let glowIntensity = 0.7;

// Camera and interaction
let cameraPos = [0, 0, 3];
let cameraRot = [0, 0];
let keys = {};
let mouseX = 0, mouseY = 0;
let isMouseDown = false;
let autoOrbit = false;
let time = 0;

// Resize canvas
function resizeCanvas() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    gl.viewport(0, 0, canvas.width, canvas.height);
}

window.addEventListener('resize', resizeCanvas);
resizeCanvas();

// Vertex shader
const vertexShaderSource = `
    attribute vec2 position;
    void main() {
        gl_Position = vec4(position, 0.0, 1.0);
    };

// Fragment shader with tesseract mandelbulb
const fragmentShaderSource = `
    precision mediump float;
    
    uniform vec2 resolution;
    uniform float time;
    uniform vec3 cameraPos;
    uniform vec2 cameraRot;
    uniform float bassLevel;
    uniform float midLevel;
    uniform float trebleLevel;
    uniform float musicReactivity;
    uniform float spikeIntensity;
    uniform float transformSpeed;
    uniform float colorShift;
    uniform float glowIntensity;
    
    // Box distance function
    float sdBox(vec3 p, vec3 b) {
        vec3 q = abs(p) - b;
        return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0);
    }

    // Tesseract with spikes
    float tesseractSpikes(vec3 pos) {
        vec3 p = pos;

        // Audio-reactive rotation
        float rotSpeed = time * transformSpeed + bassLevel * musicReactivity * 3.0;
        float c = cos(rotSpeed);
        float s = sin(rotSpeed);

        // Rotate around multiple axes
        vec2 tempXZ = vec2(p.x * c - p.z * s, p.x * s + p.z * c);
        p.x = tempXZ.x;
        p.z = tempXZ.y;

        vec2 tempXY = vec2(p.x * c - p.y * s, p.x * s + p.y * c);
        p.x = tempXY.x;
        p.y = tempXY.y;

        // Audio-reactive scaling
        float scale = 1.0 + bassLevel * musicReactivity * 0.5;
        p /= scale;

        // Main tesseract structure (nested cubes)
        float outer = sdBox(p, vec3(1.2));
        float inner = sdBox(p * 1.3, vec3(0.8));
        float tesseract = min(outer, inner);

        // Add spikes
        float spikeLength = spikeIntensity * 2.0 + trebleLevel * musicReactivity;

        // Spikes in 6 directions
        vec3 ap = abs(p);
        float maxAxis = max(ap.x, max(ap.y, ap.z));

        if (maxAxis > 1.2 && maxAxis < 1.2 + spikeLength) {
            vec3 spikePos = p;
            if (ap.x == maxAxis) spikePos.x = sign(p.x) * 1.2;
            if (ap.y == maxAxis) spikePos.y = sign(p.y) * 1.2;
            if (ap.z == maxAxis) spikePos.z = sign(p.z) * 1.2;

            float spikeRadius = (1.2 + spikeLength - maxAxis) * 0.4;
            spikeRadius *= 1.0 + trebleLevel * musicReactivity * 0.3;

            float spike = length(p - spikePos) - spikeRadius;
            tesseract = min(tesseract, spike);
        }

        return tesseract * scale;
    }
    
    // Ray direction calculation
    vec3 getRayDir(vec2 uv, vec3 camPos, vec2 camRot) {
        float cosY = cos(camRot.y);
        float sinY = sin(camRot.y);
        float cosX = cos(camRot.x);
        float sinX = sin(camRot.x);
        
        vec3 forward = vec3(sinY * cosX, -sinX, cosY * cosX);
        vec3 right = vec3(cosY, 0, -sinY);
        vec3 up = cross(right, forward);
        
        return normalize(forward + uv.x * right + uv.y * up);
    }
    
    // Normal estimation
    vec3 estimateNormal(vec3 p) {
        float d = 0.01;
        return normalize(vec3(
            tesseractSpikes(p + vec3(d,0,0)) - tesseractSpikes(p - vec3(d,0,0)),
            tesseractSpikes(p + vec3(0,d,0)) - tesseractSpikes(p - vec3(0,d,0)),
            tesseractSpikes(p + vec3(0,0,d)) - tesseractSpikes(p - vec3(0,0,d))
        ));
    }
    
    void main() {
        vec2 uv = (gl_FragCoord.xy * 2.0 - resolution) / min(resolution.x, resolution.y);
        vec3 rayDir = getRayDir(uv, cameraPos, cameraRot);
        
        float t = 0.0;
        vec3 col = vec3(0.0);
        bool hit = false;
        
        // Raymarching
        for(int i = 0; i < 100; i++) {
            vec3 pos = cameraPos + rayDir * t;
            float d = tesseractSpikes(pos);

            if(d < 0.02) {
                // Hit surface - calculate lighting and color
                vec3 normal = estimateNormal(pos);
                vec3 lightDir = normalize(vec3(1, 1, 1));
                
                float diff = max(dot(normal, lightDir), 0.0);
                float spec = pow(max(dot(reflect(-lightDir, normal), -rayDir), 0.0), 32.0);
                
                // Audio-reactive colors with better variety
                float hue = colorShift * 6.28 + time * transformSpeed * 2.0 + bassLevel * musicReactivity * 4.0;
                vec3 baseColor = vec3(
                    0.3 + 0.7 * sin(hue),
                    0.3 + 0.7 * sin(hue + 2.09),
                    0.3 + 0.7 * sin(hue + 4.18)
                );

                // Enhanced with audio frequencies for more color variation
                baseColor.r += bassLevel * musicReactivity * 0.3;
                baseColor.g += midLevel * musicReactivity * 0.3;
                baseColor.b += trebleLevel * musicReactivity * 0.3;
                
                col = baseColor * (diff * 0.8 + 0.2) + vec3(1.0) * spec * 0.5;
                
                // Add glow effect
                float glow = glowIntensity * (1.0 + bassLevel * musicReactivity);
                col += baseColor * glow * 0.3;
                
                hit = true;
                break;
            }
            
            t += d;
            if(t > 20.0) break;
        }
        
        // Background with subtle grid
        if (!hit) {
            float grid = 0.0;
            vec3 gridPos = cameraPos + rayDir * 10.0;
            grid += smoothstep(0.02, 0.0, abs(fract(gridPos.x) - 0.5));
            grid += smoothstep(0.02, 0.0, abs(fract(gridPos.y) - 0.5));
            grid += smoothstep(0.02, 0.0, abs(fract(gridPos.z) - 0.5));
            
            vec3 bgColor = vec3(0.05, 0.1, 0.2) + vec3(0.1, 0.2, 0.4) * grid * 0.1;
            col = bgColor;
        }
        
        // Add atmospheric glow around the fractal
        float distToCenter = length(cameraPos + rayDir * t);
        float atmosphericGlow = exp(-distToCenter * 0.2) * glowIntensity * 0.1;
        col += vec3(0.2, 0.4, 0.8) * atmosphericGlow;
        
        gl_FragColor = vec4(col, 1.0);
    }
;

// Shader compilation
function createShader(gl, type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('Shader compilation error:', gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
    }

    return shader;
}

function createProgram(gl, vertexShader, fragmentShader) {
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('Program linking error:', gl.getProgramInfoLog(program));
        gl.deleteProgram(program);
        return null;
    }

    return program;
}

// Initialize WebGL
const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);

if (!vertexShader || !fragmentShader) {
    console.error('Shader compilation failed');
    document.body.innerHTML = '<h1 style="color: white; text-align: center; margin-top: 50px;">Shader compilation failed. Check console for details.</h1>';
}

const program = createProgram(gl, vertexShader, fragmentShader);

if (!program) {
    console.error('Program creation failed');
    document.body.innerHTML = '<h1 style="color: white; text-align: center; margin-top: 50px;">WebGL program creation failed. Check console for details.</h1>';
}

// Create fullscreen quad
const positions = new Float32Array([
    -1, -1,
     1, -1,
    -1,  1,
     1,  1
]);

const positionBuffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

const positionLocation = gl.getAttribLocation(program, 'position');
gl.enableVertexAttribArray(positionLocation);
gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

// Get uniform locations
const uniforms = {
    resolution: gl.getUniformLocation(program, 'resolution'),
    time: gl.getUniformLocation(program, 'time'),
    cameraPos: gl.getUniformLocation(program, 'cameraPos'),
    cameraRot: gl.getUniformLocation(program, 'cameraRot'),
    bassLevel: gl.getUniformLocation(program, 'bassLevel'),
    midLevel: gl.getUniformLocation(program, 'midLevel'),
    trebleLevel: gl.getUniformLocation(program, 'trebleLevel'),
    musicReactivity: gl.getUniformLocation(program, 'musicReactivity'),
    spikeIntensity: gl.getUniformLocation(program, 'spikeIntensity'),
    transformSpeed: gl.getUniformLocation(program, 'transformSpeed'),
    colorShift: gl.getUniformLocation(program, 'colorShift'),
    glowIntensity: gl.getUniformLocation(program, 'glowIntensity')
};

// Audio setup functions
async function startMicrophone() {
    try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyser = audioContext.createAnalyser();
        analyser.fftSize = 512;
        dataArray = new Uint8Array(analyser.frequencyBinCount);

        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        microphone = audioContext.createMediaStreamSource(stream);
        microphone.connect(analyser);

        document.getElementById('startAudio').disabled = true;
        document.getElementById('stopAudio').disabled = false;

        console.log('Microphone started');
    } catch (error) {
        console.error('Microphone access denied:', error);
        alert('Microphone access denied. The visualization will work without audio input.');
    }
}

function stopAudio() {
    if (microphone) {
        microphone.disconnect();
        microphone = null;
    }
    if (audioContext) {
        audioContext.close();
        audioContext = null;
    }

    document.getElementById('startAudio').disabled = false;
    document.getElementById('stopAudio').disabled = true;

    bassLevel = 0;
    midLevel = 0;
    trebleLevel = 0;
}

function loadAudioFile(event) {
    const file = event.target.files[0];
    if (!file) return;

    stopAudio();

    audioContext = new (window.AudioContext || window.webkitAudioContext)();
    analyser = audioContext.createAnalyser();
    analyser.fftSize = 512;
    dataArray = new Uint8Array(analyser.frequencyBinCount);

    const audio = new Audio();
    audio.src = URL.createObjectURL(file);
    audio.loop = true;
    audio.crossOrigin = 'anonymous';

    audioSource = audioContext.createMediaElementSource(audio);
    audioSource.connect(analyser);
    analyser.connect(audioContext.destination);

    audio.play();

    document.getElementById('startAudio').disabled = true;
    document.getElementById('stopAudio').disabled = false;
}

// Process audio data
function processAudio() {
    if (analyser && dataArray) {
        analyser.getByteFrequencyData(dataArray);

        // Calculate frequency bands
        const bassEnd = Math.floor(dataArray.length * 0.1);
        const midEnd = Math.floor(dataArray.length * 0.5);

        let bassSum = 0, midSum = 0, trebleSum = 0;

        for (let i = 0; i < bassEnd; i++) {
            bassSum += dataArray[i];
        }
        for (let i = bassEnd; i < midEnd; i++) {
            midSum += dataArray[i];
        }
        for (let i = midEnd; i < dataArray.length; i++) {
            trebleSum += dataArray[i];
        }

        bassLevel = (bassSum / bassEnd) / 255;
        midLevel = (midSum / (midEnd - bassEnd)) / 255;
        trebleLevel = (trebleSum / (dataArray.length - midEnd)) / 255;
    }
}

// Input handling
function handleInput() {
    const speed = 0.1;

    if (keys['w'] || keys['W']) {
        cameraPos[0] += Math.sin(cameraRot[1]) * speed;
        cameraPos[2] += Math.cos(cameraRot[1]) * speed;
    }
    if (keys['s'] || keys['S']) {
        cameraPos[0] -= Math.sin(cameraRot[1]) * speed;
        cameraPos[2] -= Math.cos(cameraRot[1]) * speed;
    }
    if (keys['a'] || keys['A']) {
        cameraPos[0] -= Math.cos(cameraRot[1]) * speed;
        cameraPos[2] += Math.sin(cameraRot[1]) * speed;
    }
    if (keys['d'] || keys['D']) {
        cameraPos[0] += Math.cos(cameraRot[1]) * speed;
        cameraPos[2] -= Math.sin(cameraRot[1]) * speed;
    }
    if (keys['q'] || keys['Q']) {
        cameraPos[1] -= speed;
    }
    if (keys['e'] || keys['E']) {
        cameraPos[1] += speed;
    }

    // Auto-orbit
    if (autoOrbit) {
        const radius = Math.sqrt(cameraPos[0] * cameraPos[0] + cameraPos[2] * cameraPos[2]);
        const angle = Math.atan2(cameraPos[0], cameraPos[2]) + 0.01;
        cameraPos[0] = Math.sin(angle) * radius;
        cameraPos[2] = Math.cos(angle) * radius;

        // Look at center
        cameraRot[1] = angle + Math.PI;
        cameraRot[0] = Math.atan2(-cameraPos[1], radius) * 0.5;
    }
}

// Event listeners
document.addEventListener('keydown', (e) => {
    keys[e.key] = true;
    if (e.key === ' ') {
        e.preventDefault();
        autoOrbit = !autoOrbit;
    }
});

document.addEventListener('keyup', (e) => {
    keys[e.key] = false;
});

canvas.addEventListener('mousedown', (e) => {
    isMouseDown = true;
    mouseX = e.clientX;
    mouseY = e.clientY;
});

canvas.addEventListener('mouseup', () => {
    isMouseDown = false;
});

canvas.addEventListener('mousemove', (e) => {
    if (isMouseDown && !autoOrbit) {
        const deltaX = e.clientX - mouseX;
        const deltaY = e.clientY - mouseY;

        cameraRot[1] += deltaX * 0.005;
        cameraRot[0] += deltaY * 0.005;

        cameraRot[0] = Math.max(-Math.PI/2, Math.min(Math.PI/2, cameraRot[0]));

        mouseX = e.clientX;
        mouseY = e.clientY;
    }
});

// Control event listeners
document.getElementById('musicReactivity').addEventListener('input', (e) => {
    musicReactivity = e.target.value / 100;
    document.getElementById('reactivityValue').textContent = e.target.value;
});

document.getElementById('spikeIntensity').addEventListener('input', (e) => {
    spikeIntensity = e.target.value / 100;
    document.getElementById('spikeValue').textContent = e.target.value;
});

document.getElementById('transformSpeed').addEventListener('input', (e) => {
    transformSpeed = e.target.value / 100;
    document.getElementById('speedValue').textContent = e.target.value;
});

document.getElementById('colorShift').addEventListener('input', (e) => {
    colorShift = e.target.value / 100;
    document.getElementById('colorValue').textContent = e.target.value;
});

document.getElementById('glowIntensity').addEventListener('input', (e) => {
    glowIntensity = e.target.value / 100;
    document.getElementById('glowValue').textContent = e.target.value;
});

document.getElementById('startAudio').addEventListener('click', startMicrophone);
document.getElementById('stopAudio').addEventListener('click', stopAudio);
document.getElementById('audioFile').addEventListener('change', loadAudioFile);

// Main render loop
function render() {
    time += 0.016;

    processAudio();
    handleInput();

    gl.useProgram(program);

    // Set uniforms
    gl.uniform2f(uniforms.resolution, canvas.width, canvas.height);
    gl.uniform1f(uniforms.time, time);
    gl.uniform3f(uniforms.cameraPos, cameraPos[0], cameraPos[1], cameraPos[2]);
    gl.uniform2f(uniforms.cameraRot, cameraRot[0], cameraRot[1]);
    gl.uniform1f(uniforms.bassLevel, bassLevel);
    gl.uniform1f(uniforms.midLevel, midLevel);
    gl.uniform1f(uniforms.trebleLevel, trebleLevel);
    gl.uniform1f(uniforms.musicReactivity, musicReactivity);
    gl.uniform1f(uniforms.spikeIntensity, spikeIntensity);
    gl.uniform1f(uniforms.transformSpeed, transformSpeed);
    gl.uniform1f(uniforms.colorShift, colorShift);
    gl.uniform1f(uniforms.glowIntensity, glowIntensity);

    // Draw
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);

    requestAnimationFrame(render);
}

// Start the visualization
render();

</script>
</body>
</html>
