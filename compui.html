<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Sci-Fi Geometric Terminal</title>
<style>
    body {
        margin: 0;
        background: #000;
        overflow: hidden;
        color: #0ff;
        font-family: monospace;
    }
    #hud {
        position: fixed;
        top: 0;
        left: 0;
    }
    .terminal {
        position: absolute;
        bottom: 20px;
        left: 20px;
        padding: 10px;
        background: rgba(0,0,0,0.4);
        border: 1px solid #0ff;
        box-shadow: 0 0 15px #0ff;
        max-width: 500px;
        font-size: 14px;
        white-space: pre-wrap;
    }
    .cursor {
        display: inline-block;
        background: #0ff;
        width: 8px;
        height: 14px;
        animation: blink 1s steps(2, start) infinite;
    }
    @keyframes blink {
        to { visibility: hidden; }
    }
</style>
</head>
<body>
<canvas id="hud"></canvas>
<div class="terminal" id="terminal"></div>

<script>
const canvas = document.getElementById("hud");
const ctx = canvas.getContext("2d");
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;

function resize() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
}
window.addEventListener("resize", resize);

let angle = 0;

// Draw rotating polygons & rings
function drawHUD() {
    ctx.clearRect(0,0,canvas.width,canvas.height);

    ctx.save();
    ctx.translate(canvas.width/2, canvas.height/2);

    // Outer radar circle
    ctx.strokeStyle = "rgba(0,255,255,0.5)";
    ctx.lineWidth = 1;
    for (let r = 100; r <= 300; r += 50) {
        ctx.beginPath();
        ctx.arc(0, 0, r, 0, Math.PI*2);
        ctx.stroke();
    }

    // Rotating polygon
    ctx.rotate(angle);
    ctx.beginPath();
    const sides = 6;
    const radius = 150;
    for (let i = 0; i < sides; i++) {
        const x = radius * Math.cos((i / sides) * 2 * Math.PI);
        const y = radius * Math.sin((i / sides) * 2 * Math.PI);
        ctx.lineTo(x, y);
    }
    ctx.closePath();
    ctx.stroke();

    // Rotating sweep line
    ctx.rotate(angle * 2);
    ctx.beginPath();
    ctx.moveTo(0,0);
    ctx.lineTo(300,0);
    ctx.stroke();

    ctx.restore();

    angle += 0.01;
    requestAnimationFrame(drawHUD);
}
drawHUD();

// Terminal typing effect
const terminal = document.getElementById("terminal");
const lines = [
    ">> SYSTEM BOOT COMPLETE",
    ">> Scanning for anomalies...",
    ">> 5 DATA NODES ACTIVE",
    ">> Generating visual feed...",
    ">> Status: ONLINE"
];

let lineIndex = 0;
let charIndex = 0;
let buffer = "";

function typeLine() {
    if (lineIndex < lines.length) {
        if (charIndex < lines[lineIndex].length) {
            buffer += lines[lineIndex][charIndex];
            terminal.innerHTML = buffer + '<span class="cursor"></span>';
            charIndex++;
            setTimeout(typeLine, 40);
        } else {
            buffer += "\n";
            terminal.innerHTML = buffer;
            charIndex = 0;
            lineIndex++;
            setTimeout(typeLine, 200);
        }
    } else {
        terminal.innerHTML += '<span class="cursor"></span>';
    }
}
typeLine();
</script>
</body>
</html>
