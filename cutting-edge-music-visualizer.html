<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cutting Edge Music Visualizer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            color: white;
        }

        #canvas {
            display: block;
            width: 100vw;
            height: 100vh;
            background: radial-gradient(circle at center, #1a1a2e 0%, #000 100%);
        }

        .controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #ccc;
        }

        input[type="file"] {
            width: 100%;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            color: white;
            font-size: 12px;
        }

        input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }

        select {
            width: 100%;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            color: white;
            font-size: 12px;
        }

        button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .info {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 12px;
            max-width: 300px;
        }

        .fps {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>
    
    <div class="controls">
        <div class="control-group">
            <label>Audio File</label>
            <input type="file" id="audioFile" accept="audio/*">
        </div>
        
        <div class="control-group">
            <button id="playPause">Play / Pause</button>
        </div>
        
        <div class="control-group">
            <label>Visualization Mode</label>
            <select id="visualMode">
                <option value="particles">Particle Storm</option>
                <option value="waveform">3D Waveform</option>
                <option value="spectrum">Frequency Spectrum</option>
                <option value="tunnel">Audio Tunnel</option>
                <option value="galaxy">Galaxy</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>Sensitivity: <span id="sensitivityValue">50</span></label>
            <input type="range" id="sensitivity" min="1" max="100" value="50">
        </div>
        
        <div class="control-group">
            <label>Color Mode</label>
            <select id="colorMode">
                <option value="rainbow">Rainbow</option>
                <option value="fire">Fire</option>
                <option value="ocean">Ocean</option>
                <option value="neon">Neon</option>
                <option value="monochrome">Monochrome</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>Particle Count: <span id="particleCountValue">1000</span></label>
            <input type="range" id="particleCount" min="100" max="5000" value="1000" step="100">
        </div>
    </div>
    
    <div class="fps" id="fps">FPS: 0</div>
    
    <div class="info">
        <h3>🎵 Cutting Edge Music Visualizer</h3>
        <p>Upload an audio file and watch the magic happen!</p>
        <p><strong>Features:</strong></p>
        <ul>
            <li>Real-time audio analysis</li>
            <li>WebGL 3D graphics</li>
            <li>Multiple visualization modes</li>
            <li>Particle systems</li>
            <li>Dynamic color schemes</li>
        </ul>
    </div>

    <script>
        class MusicVisualizer {
            constructor() {
                this.canvas = document.getElementById('canvas');
                this.ctx = this.canvas.getContext('webgl') || this.canvas.getContext('experimental-webgl');
                this.audio = new Audio();
                this.audioContext = null;
                this.analyser = null;
                this.dataArray = null;
                this.source = null;
                
                this.particles = [];
                this.time = 0;
                this.lastTime = 0;
                this.fps = 0;
                this.frameCount = 0;
                
                this.settings = {
                    visualMode: 'particles',
                    sensitivity: 50,
                    colorMode: 'rainbow',
                    particleCount: 1000
                };
                
                this.init();
                this.setupEventListeners();
                this.animate();
            }
            
            init() {
                this.resizeCanvas();
                this.initWebGL();
                this.initParticles();
                window.addEventListener('resize', () => this.resizeCanvas());
            }
            
            resizeCanvas() {
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;
                if (this.ctx) {
                    this.ctx.viewport(0, 0, this.canvas.width, this.canvas.height);
                }
            }
            
            initWebGL() {
                if (!this.ctx) {
                    console.error('WebGL not supported');
                    return;
                }
                
                this.ctx.enable(this.ctx.BLEND);
                this.ctx.blendFunc(this.ctx.SRC_ALPHA, this.ctx.ONE_MINUS_SRC_ALPHA);
                this.ctx.clearColor(0.0, 0.0, 0.0, 1.0);
            }
            
            initParticles() {
                this.particles = [];
                for (let i = 0; i < this.settings.particleCount; i++) {
                    this.particles.push({
                        x: Math.random() * 2 - 1,
                        y: Math.random() * 2 - 1,
                        z: Math.random() * 2 - 1,
                        vx: (Math.random() - 0.5) * 0.02,
                        vy: (Math.random() - 0.5) * 0.02,
                        vz: (Math.random() - 0.5) * 0.02,
                        size: Math.random() * 3 + 1,
                        life: Math.random(),
                        originalSize: Math.random() * 3 + 1
                    });
                }
            }
            
            setupEventListeners() {
                document.getElementById('audioFile').addEventListener('change', (e) => {
                    this.loadAudio(e.target.files[0]);
                });
                
                document.getElementById('playPause').addEventListener('click', () => {
                    this.togglePlayPause();
                });
                
                document.getElementById('visualMode').addEventListener('change', (e) => {
                    this.settings.visualMode = e.target.value;
                });
                
                document.getElementById('sensitivity').addEventListener('input', (e) => {
                    this.settings.sensitivity = parseInt(e.target.value);
                    document.getElementById('sensitivityValue').textContent = e.target.value;
                });
                
                document.getElementById('colorMode').addEventListener('change', (e) => {
                    this.settings.colorMode = e.target.value;
                });
                
                document.getElementById('particleCount').addEventListener('input', (e) => {
                    this.settings.particleCount = parseInt(e.target.value);
                    document.getElementById('particleCountValue').textContent = e.target.value;
                    this.initParticles();
                });
            }
            
            loadAudio(file) {
                if (!file) return;
                
                const url = URL.createObjectURL(file);
                this.audio.src = url;
                
                if (!this.audioContext) {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.analyser = this.audioContext.createAnalyser();
                    this.analyser.fftSize = 2048;
                    this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
                    
                    this.source = this.audioContext.createMediaElementSource(this.audio);
                    this.source.connect(this.analyser);
                    this.analyser.connect(this.audioContext.destination);
                }
            }
            
            togglePlayPause() {
                if (this.audio.paused) {
                    this.audio.play();
                    if (this.audioContext && this.audioContext.state === 'suspended') {
                        this.audioContext.resume();
                    }
                } else {
                    this.audio.pause();
                }
            }
            
            getColorFromMode(index, total, intensity) {
                const hue = (index / total) * 360;
                const saturation = 100;
                const lightness = 50 + intensity * 50;
                
                switch (this.settings.colorMode) {
                    case 'rainbow':
                        return this.hslToRgb(hue, saturation, lightness);
                    case 'fire':
                        return this.hslToRgb(Math.min(60, hue), 100, lightness);
                    case 'ocean':
                        return this.hslToRgb(180 + hue * 0.5, 80, lightness);
                    case 'neon':
                        return this.hslToRgb(hue, 100, Math.min(80, lightness + 20));
                    case 'monochrome':
                        const gray = intensity * 255;
                        return [gray, gray, gray];
                    default:
                        return this.hslToRgb(hue, saturation, lightness);
                }
            }
            
            hslToRgb(h, s, l) {
                h /= 360;
                s /= 100;
                l /= 100;
                
                const hue2rgb = (p, q, t) => {
                    if (t < 0) t += 1;
                    if (t > 1) t -= 1;
                    if (t < 1/6) return p + (q - p) * 6 * t;
                    if (t < 1/2) return q;
                    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                    return p;
                };
                
                let r, g, b;
                if (s === 0) {
                    r = g = b = l;
                } else {
                    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                    const p = 2 * l - q;
                    r = hue2rgb(p, q, h + 1/3);
                    g = hue2rgb(p, q, h);
                    b = hue2rgb(p, q, h - 1/3);
                }
                
                return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
            }
            
            drawParticles() {
                if (!this.dataArray) return;
                
                this.analyser.getByteFrequencyData(this.dataArray);
                
                const canvas2d = document.createElement('canvas');
                canvas2d.width = this.canvas.width;
                canvas2d.height = this.canvas.height;
                const ctx2d = canvas2d.getContext('2d');
                
                ctx2d.fillStyle = 'rgba(0, 0, 0, 0.1)';
                ctx2d.fillRect(0, 0, canvas2d.width, canvas2d.height);
                
                const centerX = canvas2d.width / 2;
                const centerY = canvas2d.height / 2;
                
                this.particles.forEach((particle, i) => {
                    const freqIndex = Math.floor((i / this.particles.length) * this.dataArray.length);
                    const intensity = this.dataArray[freqIndex] / 255;
                    const sensitivity = this.settings.sensitivity / 50;
                    
                    // Update particle based on audio
                    particle.size = particle.originalSize + intensity * 10 * sensitivity;
                    particle.life += intensity * 0.1;
                    
                    // Move particles
                    particle.x += particle.vx + intensity * 0.01 * sensitivity;
                    particle.y += particle.vy + intensity * 0.01 * sensitivity;
                    particle.z += particle.vz;
                    
                    // Wrap around screen
                    if (particle.x > 1) particle.x = -1;
                    if (particle.x < -1) particle.x = 1;
                    if (particle.y > 1) particle.y = -1;
                    if (particle.y < -1) particle.y = 1;
                    
                    // Convert 3D to 2D
                    const scale = 1 / (1 + particle.z * 0.5);
                    const x = centerX + particle.x * centerX * scale;
                    const y = centerY + particle.y * centerY * scale;
                    
                    // Draw particle
                    const color = this.getColorFromMode(i, this.particles.length, intensity);
                    
                    ctx2d.beginPath();
                    ctx2d.arc(x, y, particle.size * scale, 0, Math.PI * 2);
                    ctx2d.fillStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${intensity * 0.8 + 0.2})`;
                    ctx2d.fill();
                    
                    // Add glow effect
                    ctx2d.shadowBlur = particle.size * 2;
                    ctx2d.shadowColor = `rgba(${color[0]}, ${color[1]}, ${color[2]}, 0.5)`;
                });
                
                // Copy to WebGL canvas
                this.ctx.clear(this.ctx.COLOR_BUFFER_BIT);
                const texture = this.ctx.createTexture();
                this.ctx.bindTexture(this.ctx.TEXTURE_2D, texture);
                this.ctx.texImage2D(this.ctx.TEXTURE_2D, 0, this.ctx.RGBA, this.ctx.RGBA, this.ctx.UNSIGNED_BYTE, canvas2d);
                this.ctx.texParameteri(this.ctx.TEXTURE_2D, this.ctx.TEXTURE_MIN_FILTER, this.ctx.LINEAR);
                this.ctx.texParameteri(this.ctx.TEXTURE_2D, this.ctx.TEXTURE_MAG_FILTER, this.ctx.LINEAR);
            }
            
            drawWaveform() {
                if (!this.dataArray) return;
                
                this.analyser.getByteTimeDomainData(this.dataArray);
                
                const canvas2d = document.createElement('canvas');
                canvas2d.width = this.canvas.width;
                canvas2d.height = this.canvas.height;
                const ctx2d = canvas2d.getContext('2d');
                
                ctx2d.fillStyle = 'rgba(0, 0, 0, 0.1)';
                ctx2d.fillRect(0, 0, canvas2d.width, canvas2d.height);
                
                const centerY = canvas2d.height / 2;
                const amplitude = canvas2d.height / 4;
                
                ctx2d.strokeStyle = '#00ff88';
                ctx2d.lineWidth = 2;
                ctx2d.beginPath();
                
                for (let i = 0; i < this.dataArray.length; i++) {
                    const x = (i / this.dataArray.length) * canvas2d.width;
                    const y = centerY + ((this.dataArray[i] - 128) / 128) * amplitude;
                    
                    if (i === 0) {
                        ctx2d.moveTo(x, y);
                    } else {
                        ctx2d.lineTo(x, y);
                    }
                }
                
                ctx2d.stroke();
                
                // 3D effect
                for (let layer = 0; layer < 5; layer++) {
                    const offset = layer * 20;
                    const alpha = 1 - (layer * 0.2);
                    
                    ctx2d.strokeStyle = `rgba(0, 255, 136, ${alpha})`;
                    ctx2d.lineWidth = 3 - layer * 0.5;
                    ctx2d.beginPath();
                    
                    for (let i = 0; i < this.dataArray.length; i++) {
                        const x = (i / this.dataArray.length) * canvas2d.width;
                        const y = centerY + ((this.dataArray[i] - 128) / 128) * amplitude + offset;
                        
                        if (i === 0) {
                            ctx2d.moveTo(x, y);
                        } else {
                            ctx2d.lineTo(x, y);
                        }
                    }
                    
                    ctx2d.stroke();
                }
                
                this.copyToWebGL(canvas2d);
            }
            
            drawSpectrum() {
                if (!this.dataArray) return;
                
                this.analyser.getByteFrequencyData(this.dataArray);
                
                const canvas2d = document.createElement('canvas');
                canvas2d.width = this.canvas.width;
                canvas2d.height = this.canvas.height;
                const ctx2d = canvas2d.getContext('2d');
                
                ctx2d.fillStyle = 'rgba(0, 0, 0, 0.1)';
                ctx2d.fillRect(0, 0, canvas2d.width, canvas2d.height);
                
                const barWidth = canvas2d.width / this.dataArray.length;
                
                for (let i = 0; i < this.dataArray.length; i++) {
                    const barHeight = (this.dataArray[i] / 255) * canvas2d.height;
                    const color = this.getColorFromMode(i, this.dataArray.length, this.dataArray[i] / 255);
                    
                    ctx2d.fillStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, 0.8)`;
                    ctx2d.fillRect(i * barWidth, canvas2d.height - barHeight, barWidth, barHeight);
                    
                    // Add glow
                    ctx2d.shadowBlur = 10;
                    ctx2d.shadowColor = `rgba(${color[0]}, ${color[1]}, ${color[2]}, 0.5)`;
                }
                
                this.copyToWebGL(canvas2d);
            }
            
            drawTunnel() {
                if (!this.dataArray) return;
                
                this.analyser.getByteFrequencyData(this.dataArray);
                
                const canvas2d = document.createElement('canvas');
                canvas2d.width = this.canvas.width;
                canvas2d.height = this.canvas.height;
                const ctx2d = canvas2d.getContext('2d');
                
                ctx2d.fillStyle = 'rgba(0, 0, 0, 0.05)';
                ctx2d.fillRect(0, 0, canvas2d.width, canvas2d.height);
                
                const centerX = canvas2d.width / 2;
                const centerY = canvas2d.height / 2;
                
                for (let i = 0; i < this.dataArray.length; i++) {
                    const angle = (i / this.dataArray.length) * Math.PI * 2;
                    const intensity = this.dataArray[i] / 255;
                    const radius = 50 + intensity * 200;
                    
                    const x = centerX + Math.cos(angle + this.time * 0.01) * radius;
                    const y = centerY + Math.sin(angle + this.time * 0.01) * radius;
                    
                    const color = this.getColorFromMode(i, this.dataArray.length, intensity);
                    
                    ctx2d.beginPath();
                    ctx2d.arc(x, y, intensity * 10 + 2, 0, Math.PI * 2);
                    ctx2d.fillStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${intensity})`;
                    ctx2d.fill();
                    
                    // Connect to center
                    ctx2d.beginPath();
                    ctx2d.moveTo(centerX, centerY);
                    ctx2d.lineTo(x, y);
                    ctx2d.strokeStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${intensity * 0.3})`;
                    ctx2d.lineWidth = intensity * 3;
                    ctx2d.stroke();
                }
                
                this.copyToWebGL(canvas2d);
            }
            
            drawGalaxy() {
                if (!this.dataArray) return;
                
                this.analyser.getByteFrequencyData(this.dataArray);
                
                const canvas2d = document.createElement('canvas');
                canvas2d.width = this.canvas.width;
                canvas2d.height = this.canvas.height;
                const ctx2d = canvas2d.getContext('2d');
                
                ctx2d.fillStyle = 'rgba(0, 0, 0, 0.02)';
                ctx2d.fillRect(0, 0, canvas2d.width, canvas2d.height);
                
                const centerX = canvas2d.width / 2;
                const centerY = canvas2d.height / 2;
                
                for (let i = 0; i < this.dataArray.length; i++) {
                    const intensity = this.dataArray[i] / 255;
                    const angle = (i / this.dataArray.length) * Math.PI * 4 + this.time * 0.005;
                    const spiral = i * 2;
                    const radius = spiral + intensity * 100;
                    
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    const color = this.getColorFromMode(i, this.dataArray.length, intensity);
                    const size = intensity * 8 + 1;
                    
                    ctx2d.beginPath();
                    ctx2d.arc(x, y, size, 0, Math.PI * 2);
                    ctx2d.fillStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${intensity * 0.8 + 0.2})`;
                    ctx2d.fill();
                    
                    // Add stars
                    if (intensity > 0.7) {
                        ctx2d.shadowBlur = size * 3;
                        ctx2d.shadowColor = `rgba(${color[0]}, ${color[1]}, ${color[2]}, 1)`;
                    }
                }
                
                this.copyToWebGL(canvas2d);
            }
            
            copyToWebGL(canvas2d) {
                this.ctx.clear(this.ctx.COLOR_BUFFER_BIT);
                
                // Create and bind texture
                const texture = this.ctx.createTexture();
                this.ctx.bindTexture(this.ctx.TEXTURE_2D, texture);
                this.ctx.texImage2D(this.ctx.TEXTURE_2D, 0, this.ctx.RGBA, this.ctx.RGBA, this.ctx.UNSIGNED_BYTE, canvas2d);
                this.ctx.texParameteri(this.ctx.TEXTURE_2D, this.ctx.TEXTURE_MIN_FILTER, this.ctx.LINEAR);
                this.ctx.texParameteri(this.ctx.TEXTURE_2D, this.ctx.TEXTURE_MAG_FILTER, this.ctx.LINEAR);
                
                // Simple quad rendering (fallback to 2D canvas overlay)
                const webglCanvas = this.canvas;
                const overlayCanvas = document.getElementById('overlay') || document.createElement('canvas');
                if (!document.getElementById('overlay')) {
                    overlayCanvas.id = 'overlay';
                    overlayCanvas.style.position = 'absolute';
                    overlayCanvas.style.top = '0';
                    overlayCanvas.style.left = '0';
                    overlayCanvas.style.pointerEvents = 'none';
                    overlayCanvas.style.zIndex = '1';
                    document.body.appendChild(overlayCanvas);
                }
                
                overlayCanvas.width = canvas2d.width;
                overlayCanvas.height = canvas2d.height;
                const overlayCtx = overlayCanvas.getContext('2d');
                overlayCtx.drawImage(canvas2d, 0, 0);
            }
            
            updateFPS() {
                this.frameCount++;
                const currentTime = performance.now();
                
                if (currentTime - this.lastTime >= 1000) {
                    this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
                    document.getElementById('fps').textContent = `FPS: ${this.fps}`;
                    this.frameCount = 0;
                    this.lastTime = currentTime;
                }
            }
            
            animate() {
                this.time++;
                this.updateFPS();
                
                switch (this.settings.visualMode) {
                    case 'particles':
                        this.drawParticles();
                        break;
                    case 'waveform':
                        this.drawWaveform();
                        break;
                    case 'spectrum':
                        this.drawSpectrum();
                        break;
                    case 'tunnel':
                        this.drawTunnel();
                        break;
                    case 'galaxy':
                        this.drawGalaxy();
                        break;
                }
                
                requestAnimationFrame(() => this.animate());
            }
        }
        
        // Initialize the visualizer when the page loads
        window.addEventListener('load', () => {
            new MusicVisualizer();
        });
    </script>
</body>
</html>
