<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Intricate Music Visualizer + Recorder (Single File)</title>
  <style>
    :root {
      --bg: #0a0c10;
      --panel: #0f1320cc;
      --accent: #8ef5ff;
      --accent2: #a7ff6e;
      --muted: #6b7280;
      --text: #e5e7eb;
      --danger: #ff5a7a;
    }
    html, body {
      height: 100%;
      margin: 0;
      background: radial-gradient(1200px 800px at 70% 20%, #0d1220 0%, #0a0c10 60%, #07080c 100%);
      color: var(--text);
      font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Inter, "Helvetica Neue", Arial;
      overflow: hidden;
    }
    /* Layout */
    .wrap { position: relative; width: 100vw; height: 100vh; }
    canvas#stage { position: absolute; inset: 0; width: 100%; height: 100%; display: block; }
    .hud {
      position: absolute; top: 12px; left: 12px; right: 12px; display: grid; gap: 10px;
      grid-template-columns: 1fr auto; align-items: start; pointer-events: none;
    }
    .panel {
      pointer-events: auto; backdrop-filter: blur(10px);
      background: var(--panel); border: 1px solid #223; border-radius: 14px; box-shadow: 0 10px 30px #0008;
      padding: 12px 14px; display: grid; gap: 10px; grid-auto-flow: row;
    }
    .controls { grid-column: 1 / span 1; }
    .meters { grid-column: 2 / span 1; min-width: 260px; }
    .row { display: grid; grid-template-columns: 140px 1fr auto; gap: 10px; align-items: center; }
    .row > label { color: #b9c0d0; font-size: 12px; letter-spacing: .04em; text-transform: uppercase; }
    .row > input[type="range"] { width: 100%; }
    .row > select, .row > input[type="file"], .row > button { width: 100%; }
    .row.stack { grid-template-columns: 1fr; }
    .btns { display: grid; grid-template-columns: repeat(4, minmax(0,1fr)); gap: 8px; }
    button {
      background: linear-gradient(180deg, #171d2f, #0f1426);
      border: 1px solid #26304e; color: var(--text); padding: 10px 12px; border-radius: 10px;
      cursor: pointer; font-weight: 600; letter-spacing: .02em; transition: transform .08s ease, border-color .2s;
    }
    button:hover { transform: translateY(-1px); border-color: #3a4f7a; }
    button:active { transform: translateY(0); }
    .accent { border-color: #3aa1c7; box-shadow: inset 0 0 20px #00d1ff10; }
    .danger { border-color: #7a2a3a; background: linear-gradient(180deg, #29121a, #1b0c11); color: #ffd8e0; }
    .pill { font-size: 12px; padding: 6px 8px; }
    .toggles { display: grid; grid-template-columns: repeat(3, minmax(0,1fr)); gap: 8px; }
    .toggle {
      display: flex; align-items: center; justify-content: space-between; gap: 10px; padding: 8px 10px;
      border: 1px solid #243; background: #0d1222; border-radius: 10px; font-size: 13px;
    }
    .toggle input { accent-color: #4bf; }
    .lightbar { height: 8px; background: linear-gradient(90deg, #0ff0 0%, #0ff3 20%, #0ff 40%, #0ff3 60%, #0ff0 100%);
      filter: blur(8px) drop-shadow(0 0 12px #0ff6); border-radius: 60px; }
    .tiny { font-size: 11px; color: #aab; }
    .meterWrap { display: grid; gap: 10px; }
    .bar { height: 8px; background: #0b1222; border: 1px solid #223; border-radius: 10px; overflow: hidden; }
    .bar > i { display: block; height: 100%; width: 0%; background: linear-gradient(90deg, var(--accent), var(--accent2)); box-shadow: 0 0 16px #00fff049 inset; }
    .note { opacity: .8; }
    .credits { position: absolute; right: 12px; bottom: 10px; font-size: 12px; color: #98a; background: #0d1120b0; padding: 8px 10px; border-radius: 8px; border: 1px solid #223; }
    .help { font-size: 12px; color: #b9c0d0; }
    a { color: #aef; }
  </style>
</head>
<body>
  <div class="wrap">
    <canvas id="stage"></canvas>

    <div class="hud">
      <!-- Controls Panel -->
      <div class="panel controls">
        <div class="lightbar"></div>
        <div class="row">
          <label>Audio Source</label>
          <select id="sourceSelect">
            <option value="file">Audio File</option>
            <option value="mic">Microphone</option>
            <option value="screen">Screen / System Audio</option>
          </select>
          <button id="init" class="accent">Init Audio</button>
        </div>
        <div class="row" id="fileRow">
          <label>Load File</label>
          <input type="file" id="fileInput" accept="audio/*" />
          <button id="playPause">Play</button>
        </div>
        <div class="row">
          <label>Volume</label>
          <input type="range" id="gain" min="0" max="1.5" value="1.0" step="0.01" />
          <span class="tiny" id="gainVal">1.00×</span>
        </div>
        <div class="row">
          <label>Sensitivity</label>
          <input type="range" id="sens" min="0.2" max="4" value="1.2" step="0.05" />
          <span class="tiny" id="sensVal">1.20×</span>
        </div>
        <div class="row">
          <label>Smoothing</label>
          <input type="range" id="smooth" min="0" max="0.95" value="0.75" step="0.01" />
          <span class="tiny" id="smoothVal">0.75</span>
        </div>
        <div class="toggles">
          <div class="toggle"><span>Radial Spectrum</span><input type="checkbox" id="tRadial" checked></div>
          <div class="toggle"><span>Waveform Halo</span><input type="checkbox" id="tHalo" checked></div>
          <div class="toggle"><span>Particle Field</span><input type="checkbox" id="tParticles" checked></div>
          <div class="toggle"><span>Orbit Lines</span><input type="checkbox" id="tOrbits" checked></div>
          <div class="toggle"><span>Spectrogram Tail</span><input type="checkbox" id="tSpec" checked></div>
          <div class="toggle"><span>Bloom/Glow</span><input type="checkbox" id="tBloom" checked></div>
        </div>
        <div class="btns">
          <button id="recCanvas" class="pill">Record Canvas + App Audio</button>
          <button id="recScreen" class="pill">Record Screen + System Audio</button>
          <button id="stopRec" class="pill danger" disabled>Stop Recording</button>
          <a id="download" class="pill" href="#" download="visualizer-recording.webm" style="text-align:center; display:block; line-height:30px; border:1px solid #26304e; border-radius:10px; text-decoration:none;">Download</a>
        </div>
        <div class="help note">
          <p class="tiny">Tip: "Screen / System Audio" uses your browser's screen share. In Chrome, select a tab and enable "Share tab audio" for clean capture, or entire screen + "Share system audio" on Windows. macOS may require enabling system audio in settings.</p>
        </div>
      </div>

      <!-- Meters Panel -->
      <div class="panel meters">
        <div class="row stack"><strong>Live Levels</strong></div>
        <div class="meterWrap">
          <div class="row"><label>Bass</label><div class="bar"><i id="mBass"></i></div><span class="tiny" id="vBass">0</span></div>
          <div class="row"><label>Mid</label><div class="bar"><i id="mMid"></i></div><span class="tiny" id="vMid">0</span></div>
          <div class="row"><label>Treble</label><div class="bar"><i id="mHigh"></i></div><span class="tiny" id="vHigh">0</span></div>
          <div class="row"><label>RMS</label><div class="bar"><i id="mRms"></i></div><span class="tiny" id="vRms">0</span></div>
        </div>
      </div>
    </div>

    <div class="credits">Intricate Visualizer • WebAudio + Canvas • <span id="fps">0</span> FPS</div>
  </div>

  <audio id="player" crossorigin="anonymous" hidden></audio>

  <script>
  (() => {
    const canvas = document.getElementById('stage');
    const ctx = canvas.getContext('2d');
    const DPR = Math.min(2, window.devicePixelRatio || 1);

    let W = 0, H = 0, CX = 0, CY = 0, R = 0;
    function resize() {
      W = canvas.width = Math.floor(innerWidth * DPR);
      H = canvas.height = Math.floor(innerHeight * DPR);
      canvas.style.width = innerWidth + 'px';
      canvas.style.height = innerHeight + 'px';
      CX = W / 2; CY = H / 2; R = Math.min(W, H) * 0.35;
    }
    addEventListener('resize', resize, { passive: true });
    resize();

    // ---- Audio Graph ----
    let audioCtx, analyser, analyserWf, srcNode, gainNode, masterGain, mediaDest, 
        screenStream = null, fileBuffer = null, playing = false;

    const fftSize = 2048; // detailed
    const freq = new Uint8Array(fftSize / 2);
    const wave = new Uint8Array(fftSize);

    const ui = {
      sourceSelect: document.getElementById('sourceSelect'),
      init: document.getElementById('init'),
      fileRow: document.getElementById('fileRow'),
      fileInput: document.getElementById('fileInput'),
      playPause: document.getElementById('playPause'),
      gain: document.getElementById('gain'), gainVal: document.getElementById('gainVal'),
      sens: document.getElementById('sens'), sensVal: document.getElementById('sensVal'),
      smooth: document.getElementById('smooth'), smoothVal: document.getElementById('smoothVal'),
      toggles: {
        radial: document.getElementById('tRadial'),
        halo: document.getElementById('tHalo'),
        particles: document.getElementById('tParticles'),
        orbits: document.getElementById('tOrbits'),
        spec: document.getElementById('tSpec'),
        bloom: document.getElementById('tBloom'),
      },
      meters: {
        bass: document.getElementById('mBass'), vBass: document.getElementById('vBass'),
        mid: document.getElementById('mMid'), vMid: document.getElementById('vMid'),
        high: document.getElementById('mHigh'), vHigh: document.getElementById('vHigh'),
        rms: document.getElementById('mRms'), vRms: document.getElementById('vRms'),
      },
      recCanvas: document.getElementById('recCanvas'),
      recScreen: document.getElementById('recScreen'),
      stopRec: document.getElementById('stopRec'),
      download: document.getElementById('download'),
      player: document.getElementById('player'),
      fps: document.getElementById('fps')
    };

    ui.sourceSelect.addEventListener('change', () => {
      ui.fileRow.style.display = ui.sourceSelect.value === 'file' ? 'grid' : 'none';
    });

    ui.gain.addEventListener('input', () => ui.gainVal.textContent = (+ui.gain.value).toFixed(2) + '×');
    ui.sens.addEventListener('input', () => ui.sensVal.textContent = (+ui.sens.value).toFixed(2) + '×');
    ui.smooth.addEventListener('input', () => ui.smoothVal.textContent = (+ui.smooth.value).toFixed(2));

    ui.fileInput.addEventListener('change', async (e) => {
      const file = e.target.files?.[0];
      if (!file) return;
      const url = URL.createObjectURL(file);
      ui.player.src = url;
      ui.player.play().catch(()=>{});
      if (audioCtx && audioCtx.state === 'suspended') await audioCtx.resume();
      playing = true; ui.playPause.textContent = 'Pause';
    });

    ui.playPause.addEventListener('click', async () => {
      if (ui.sourceSelect.value !== 'file') return;
      if (!ui.player.src) return;
      if (!playing) { await ui.player.play(); playing = true; ui.playPause.textContent = 'Pause'; }
      else { ui.player.pause(); playing = false; ui.playPause.textContent = 'Play'; }
    });

    async function initAudio() {
      if (!audioCtx) audioCtx = new (window.AudioContext || window.webkitAudioContext)();

      // Clean any previous graph
      if (srcNode) try { srcNode.disconnect(); } catch(e){}

      analyser = audioCtx.createAnalyser();
      analyser.fftSize = fftSize;
      analyser.smoothingTimeConstant = parseFloat(ui.smooth.value);

      analyserWf = audioCtx.createAnalyser();
      analyserWf.fftSize = fftSize;
      analyserWf.smoothingTimeConstant = 0.5;

      gainNode = audioCtx.createGain();
      masterGain = audioCtx.createGain();
      mediaDest = audioCtx.createMediaStreamDestination();

      gainNode.gain.value = parseFloat(ui.gain.value);
      masterGain.gain.value = 1;

      const sourceKind = ui.sourceSelect.value;

      if (sourceKind === 'mic') {
        const gum = await navigator.mediaDevices.getUserMedia({ audio: { echoCancellation: false, noiseSuppression: false, autoGainControl: false } });
        srcNode = audioCtx.createMediaStreamSource(gum);
      } else if (sourceKind === 'screen') {
        screenStream = await navigator.mediaDevices.getDisplayMedia({ video: true, audio: true });
        // Use audio from screen as source; note: availability varies by OS/browser.
        const audioTracks = screenStream.getAudioTracks();
        if (audioTracks && audioTracks.length) {
          srcNode = audioCtx.createMediaStreamSource(new MediaStream(audioTracks));
        } else {
          alert('No audio track detected from screen share. Try sharing a tab with "Share tab audio" enabled, or entire screen with system audio.');
        }
      } else /* file */ {
        // Use <audio> element as source for continuous playback
        if (!ui.player.src) {
          alert('Load an audio file first.');
          return;
        }
        // Element source works well and keeps in sync with playback controls
        const stream = ui.player.captureStream?.() || ui.player.mozCaptureStream?.();
        if (stream) {
          srcNode = audioCtx.createMediaStreamSource(stream);
        } else {
          // Fallback: decode + BufferSource
          const buf = await fetch(ui.player.src).then(r => r.arrayBuffer()).then(b => audioCtx.decodeAudioData(b));
          const bs = audioCtx.createBufferSource();
          bs.buffer = buf; bs.loop = true; bs.start();
          srcNode = bs;
        }
      }

      // Connect graph: src -> gain -> [analyser, analyserWf] -> master -> (destination + mediaDest)
      srcNode.connect(gainNode);
      gainNode.connect(analyser);
      gainNode.connect(analyserWf);
      gainNode.connect(masterGain);
      masterGain.connect(audioCtx.destination);
      masterGain.connect(mediaDest);

      analyser.smoothingTimeConstant = parseFloat(ui.smooth.value);

      // UI listeners
      ui.gain.oninput = () => { if (gainNode) gainNode.gain.value = parseFloat(ui.gain.value); };
      ui.smooth.oninput = () => { if (analyser) analyser.smoothingTimeConstant = parseFloat(ui.smooth.value); };

      // Kick render loop if not already
      if (!rafId) render();
    }

    ui.init.addEventListener('click', () => initAudio().catch(err => alert(err.message)));

    // ---- Visuals ----
    // Particle system
    const MAX_PARTICLES = 800;
    const particles = [];
    function spawnParticles(n) {
      for (let i = 0; i < n; i++) {
        particles.push({
          r: (Math.random() ** 0.6) * R * 1.2 + 20,
          a: Math.random() * Math.PI * 2,
          v: (Math.random() * 0.002) + 0.0005,
          w: (Math.random() * 0.002) - 0.001,
          s: Math.random() * 2 + 0.5,
          life: Math.random() * 1,
        });
      }
    }
    spawnParticles(MAX_PARTICLES);

    // Spectrogram tail via offscreen buffer
    const tail = document.createElement('canvas');
    const tctx = tail.getContext('2d');

    function resizeTail() {
      tail.width = Math.max(512, Math.floor(W / 2));
      tail.height = Math.max(256, Math.floor(H / 3));
    }
    resizeTail();

    let rafId = 0, lastT = performance.now(), fpsAvg = 0;

    function render(t) {
      rafId = requestAnimationFrame(render);
      const dt = Math.max(0.001, (t - lastT) / 1000); lastT = t;

      // FPS meter
      const fps = 1 / dt; fpsAvg = fpsAvg * 0.9 + fps * 0.1; ui.fps.textContent = fpsAvg.toFixed(0);

      // Read audio
      if (analyser) {
        analyser.getByteFrequencyData(freq);
        analyserWf.getByteTimeDomainData(wave);
      } else {
        // Fill with zeros if no audio yet
        freq.fill(0); wave.fill(128);
      }

      const sens = parseFloat(ui.sens.value);
      const n = freq.length;

      // Compute bands
      const bass = bandAvg(freq, 0, Math.floor(n * 0.08)) / 255 * sens;
      const mid = bandAvg(freq, Math.floor(n * 0.08), Math.floor(n * 0.5)) / 255 * sens;
      const high = bandAvg(freq, Math.floor(n * 0.5), n) / 255 * sens;
      const rms = computeRMS(wave) * sens;

      updateMeters(bass, mid, high, rms);

      // Background gradient + subtle vignette
      ctx.clearRect(0,0,W,H);
      const g = ctx.createRadialGradient(CX, CY, Math.min(W,H)*0.05, CX, CY, Math.max(W,H)*0.8);
      g.addColorStop(0, '#0c1324');
      g.addColorStop(1, '#070a12');
      ctx.fillStyle = g; ctx.fillRect(0,0,W,H);

      // Bloom layer
      if (ui.toggles.bloom.checked) {
        ctx.globalCompositeOperation = 'lighter';
      } else {
        ctx.globalCompositeOperation = 'source-over';
      }

      // Orbits
      if (ui.toggles.orbits.checked) drawOrbits(dt, bass, mid, high);

      // Radial Spectrum Bars
      if (ui.toggles.radial.checked) drawRadial(freq, sens);

      // Waveform Halo
      if (ui.toggles.halo.checked) drawHalo(wave, sens);

      // Particles
      if (ui.toggles.particles.checked) drawParticles(dt, bass, mid, high);

      // Spectrogram tail (bottom area)
      if (ui.toggles.spec.checked) drawSpectrogram(freq);

      // Vignette overlay
      ctx.globalCompositeOperation = 'multiply';
      const vg = ctx.createRadialGradient(CX, CY, R, CX, CY, Math.max(W,H)*0.8);
      vg.addColorStop(0, 'rgba(255,255,255,0)');
      vg.addColorStop(1, 'rgba(0,0,0,0.55)');
      ctx.fillStyle = vg; ctx.fillRect(0,0,W,H);

      ctx.globalCompositeOperation = 'source-over';
    }

    function bandAvg(arr, a, b) {
      let s = 0; const len = Math.max(1, b - a);
      for (let i = a; i < b; i++) s += arr[i];
      return s / len;
    }
    function computeRMS(w) {
      let s = 0; for (let i = 0; i < w.length; i++) { const v = (w[i]-128)/128; s += v*v; }
      return Math.sqrt(s / w.length);
    }
    function updateMeters(b, m, h, r) {
      const clamp01 = v => Math.max(0, Math.min(1, v));
      const set = (el, val, txt) => { el.style.width = (clamp01(val) * 100).toFixed(1) + '%'; if (txt) txt.textContent = (clamp01(val) * 100).toFixed(0); };
      set(ui.meters.bass, b, ui.meters.vBass);
      set(ui.meters.mid, m, ui.meters.vMid);
      set(ui.meters.high, h, ui.meters.vHigh);
      set(ui.meters.rms, r, ui.meters.vRms);
    }

    function drawOrbits(dt, bass, mid, high) {
      ctx.save();
      const layers = 5;
      for (let i = 0; i < layers; i++) {
        const rad = R * (0.5 + i*0.12) + Math.sin(performance.now()/1000 + i)*8;
        ctx.strokeStyle = `hsla(${200 + i*15}, 90%, ${50 + high*20}%, 0.25)`;
        ctx.lineWidth = 1.5;
        ctx.beginPath();
        ctx.arc(CX, CY, rad, 0, Math.PI*2);
        ctx.stroke();
      }
      // orbiting nodes
      const nodes = 24;
      for (let i = 0; i < nodes; i++) {
        const angle = (i / nodes) * Math.PI*2 + performance.now()/3000 + bass*2;
        const rad = R * 0.9 + Math.sin(i*1.7 + performance.now()/700) * 20;
        const x = CX + Math.cos(angle)*rad;
        const y = CY + Math.sin(angle)*rad;
        const s = 2 + Math.sin(i + performance.now()/200) * 1.2 + high*4;
        const grd = ctx.createRadialGradient(x,y,0,x,y,s*6);
        grd.addColorStop(0, 'rgba(160,255,255,0.9)');
        grd.addColorStop(1, 'rgba(0,20,40,0)');
        ctx.fillStyle = grd; ctx.beginPath(); ctx.arc(x,y,s*2,0,Math.PI*2); ctx.fill();
      }
      ctx.restore();
    }

    function drawRadial(freq, sens) {
      ctx.save();
      const bars = 240; // detail
      const step = Math.floor(freq.length / bars);
      ctx.translate(CX, CY);
      for (let i = 0; i < bars; i++) {
        const v = freq[i*step] / 255 * sens;
        const angle = (i / bars) * Math.PI * 2;
        const len = (R * 0.35) + v * (R * 0.9);
        const x1 = Math.cos(angle) * (R * 0.25);
        const y1 = Math.sin(angle) * (R * 0.25);
        const x2 = Math.cos(angle) * (len);
        const y2 = Math.sin(angle) * (len);
        const hue = 190 + v * 80;
        ctx.strokeStyle = `hsla(${hue}, 100%, ${50 + v*25}%, ${0.35 + v*0.45})`;
        ctx.lineWidth = 2 + v * 5;
        ctx.beginPath();
        ctx.moveTo(x1,y1); ctx.lineTo(x2,y2); ctx.stroke();
      }
      ctx.restore();
    }

    function drawHalo(wave, sens) {
      ctx.save();
      ctx.translate(CX, CY);
      ctx.rotate(performance.now()/6000);
      const pts = 512;
      const step = Math.floor(wave.length / pts);
      ctx.beginPath();
      for (let i = 0; i < pts; i++) {
        const v = (wave[i*step]-128)/128 * sens;
        const rr = R * 0.4 + v * (R * 0.35);
        const a = (i/pts) * Math.PI*2;
        const x = Math.cos(a)*rr;
        const y = Math.sin(a)*rr;
        i ? ctx.lineTo(x,y) : ctx.moveTo(x,y);
      }
      ctx.closePath();
      ctx.strokeStyle = 'rgba(160,255,255,0.6)';
      ctx.lineWidth = 2;
      ctx.shadowColor = '#66ffff';
      ctx.shadowBlur = 20;
      ctx.stroke();
      ctx.restore();
    }

    function drawParticles(dt, bass, mid, high) {
      ctx.save();
      const energy = (bass*0.7 + mid*0.3);
      for (let i = 0; i < particles.length; i++) {
        const p = particles[i];
        p.a += p.v + p.w * energy*2;
        p.r += Math.sin(p.a*2 + i)*0.05 + (energy-0.2)*0.6;
        p.life += dt * (0.2 + energy*0.8);
        // wrap
        if (p.r < 10) p.r = R * 1.2;
        if (p.r > R * 1.3) p.r = 20 + Math.random() * R * 0.4;
        const x = CX + Math.cos(p.a)*p.r;
        const y = CY + Math.sin(p.a)*p.r;
        const s = p.s + energy*2;
        const alpha = 0.15 + Math.abs(Math.sin(p.life*2))*0.2 + high*0.3;
        ctx.fillStyle = `rgba(180,255,255,${alpha})`;
        ctx.beginPath(); ctx.arc(x,y,s,0,Math.PI*2); ctx.fill();
      }
      ctx.restore();
    }

    function drawSpectrogram(freq) {
      // Shift left and draw new column at right
      tctx.drawImage(tail, -1, 0);
      // Draw a vertical line encoding frequencies
      const h = tail.height;
      for (let y = 0; y < h; y++) {
        const i = Math.floor((y / h) * freq.length);
        const v = freq[i] / 255;
        tctx.fillStyle = `hsla(${190 + v*100}, 100%, ${30 + v*40}%, ${0.55})`;
        tctx.fillRect(tail.width-1, h - y, 1, 1);
      }
      // Composite at bottom of screen
      const scale = 1.6; // bigger on screen
      const ww = tail.width * scale;
      const hh = tail.height * scale;
      ctx.globalAlpha = 0.9;
      ctx.drawImage(tail, CX - ww/2, H - hh - 20*DPR, ww, hh);
      ctx.globalAlpha = 1;
    }

    // ---- Recording ----
    let recorder = null; let chunks = [];

    ui.recCanvas.addEventListener('click', async () => {
      if (!audioCtx) await initAudio();
      // Use the canvas stream + app audio (mediaDest)
      const canvasStream = canvas.captureStream(60);
      const mixed = new MediaStream([
        ...canvasStream.getVideoTracks(),
        ...mediaDest.stream.getAudioTracks()
      ]);
      startRecording(mixed);
    });

    ui.recScreen.addEventListener('click', async () => {
      // Full screen/tab with system audio (user must choose and enable audio in share sheet)
      const disp = await navigator.mediaDevices.getDisplayMedia({ video: { frameRate: 60 }, audio: true });
      startRecording(disp);
    });

    function startRecording(stream) {
      chunks = [];
      const mime = MediaRecorder.isTypeSupported('video/webm;codecs=vp9,opus') ? 'video/webm;codecs=vp9,opus' : 'video/webm';
      recorder = new MediaRecorder(stream, { mimeType: mime, videoBitsPerSecond: 8_000_000, audioBitsPerSecond: 192_000 });
      recorder.ondataavailable = (e) => { if (e.data && e.data.size) chunks.push(e.data); };
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        ui.download.href = url;
      };
      recorder.start();
      ui.stopRec.disabled = false;
    }

    ui.stopRec.addEventListener('click', () => {
      if (recorder && recorder.state !== 'inactive') {
        recorder.stop();
        ui.stopRec.disabled = true;
      }
    });

    // ---- Utilities ----
    // Handle DPR / resize-dependent elements
    const ro = new ResizeObserver(resize);
    ro.observe(document.body);
  })();
  </script>
</body>
</html>
