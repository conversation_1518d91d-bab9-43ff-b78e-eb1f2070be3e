<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Layer Bars Music Visualizer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(45deg, #0a0a0a, #0d1b0d, #1a2e1a);
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            color: #00ff00;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #canvas {
            display: block;
            background: radial-gradient(circle, #001100, #003300);
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 20, 0, 0.9);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 255, 0, 0.3);
            min-width: 250px;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.2);
        }

        .control-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #00cc00;
        }

        input[type="file"] {
            width: 100%;
            padding: 8px;
            background: rgba(0, 50, 0, 0.3);
            border: 1px solid rgba(0, 255, 0, 0.5);
            border-radius: 5px;
            color: #00ff00;
            font-size: 12px;
        }

        input[type="range"] {
            width: 100%;
            margin: 5px 0;
            -webkit-appearance: none;
            appearance: none;
            height: 6px;
            background: linear-gradient(to right, #002200, #004400);
            border-radius: 3px;
            outline: none;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: linear-gradient(45deg, #00ff00, #00cc00);
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid #008800;
            box-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
        }

        input[type="range"]::-webkit-slider-thumb:hover {
            background: linear-gradient(45deg, #00ff88, #00ff00);
            box-shadow: 0 0 12px rgba(0, 255, 0, 0.8);
        }

        input[type="range"]::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: linear-gradient(45deg, #00ff00, #00cc00);
            border-radius: 50%;
            cursor: pointer;
            border: 2px solid #008800;
            box-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
        }

        .value-display {
            font-size: 11px;
            color: #00ff88;
            text-align: right;
        }

        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: rgba(0, 255, 0, 0.8);
            font-size: 12px;
            max-width: 300px;
        }

        button {
            background: linear-gradient(45deg, #004400, #006600);
            border: 1px solid #00aa00;
            padding: 8px 16px;
            border-radius: 5px;
            color: #00ff00;
            cursor: pointer;
            font-size: 12px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 255, 0, 0.4);
            background: linear-gradient(45deg, #006600, #008800);
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
        
        <div id="controls">
            <div class="control-group">
                <label>Audio File</label>
                <input type="file" id="fileInput" accept="audio/*">
                <button id="playPause">Play/Pause</button>
            </div>
            
            <div class="control-group">
                <label>Sensitivity</label>
                <input type="range" id="sensitivity" min="0.1" max="3" step="0.1" value="1">
                <div class="value-display" id="sensitivityValue">1.0</div>
            </div>
            
            <div class="control-group">
                <label>Bar Count</label>
                <input type="range" id="barCount" min="32" max="256" step="16" value="128">
                <div class="value-display" id="barCountValue">128</div>
            </div>
            
            <div class="control-group">
                <label>Layer Spacing</label>
                <input type="range" id="layerSpacing" min="2" max="8" step="0.5" value="4">
                <div class="value-display" id="layerSpacingValue">4.0</div>
            </div>
            
            <div class="control-group">
                <label>Grid Reflection</label>
                <input type="range" id="gridReflection" min="0" max="1" step="0.1" value="0.6">
                <div class="value-display" id="gridReflectionValue">0.6</div>
            </div>
        </div>
        
        <div id="info">
            Multi-Layer Bars Visualizer<br>
            • 3 layers of frequency-reactive bars<br>
            • Reflective grid floor<br>
            • Click and drag to rotate • Scroll to zoom
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        class MultiLayerBarsVisualizer {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.audioContext = null;
                this.analyser = null;
                this.audio = null;
                this.dataArray = null;
                this.isPlaying = false;
                
                // Visualization parameters
                this.barCount = 128;
                this.sensitivity = 1.0;
                this.layerSpacing = 4.0;
                this.gridReflection = 0.6;

                // Performance optimization
                this.frameCount = 0;
                this.updateFrequency = 2; // Update every 2 frames for better performance
                this.sharedGeometry = null;
                this.materialPool = [];
                
                // 3D objects
                this.barLayers = [];
                this.gridFloor = null;
                this.reflectionGroup = null;
                
                // Camera controls
                this.mouseX = 0;
                this.mouseY = 0;
                this.isMouseDown = false;
                this.cameraRotationX = 0.3;
                this.cameraRotationY = 0;
                this.cameraDistance = 20;
                
                this.init();
                this.setupEventListeners();
                this.animate();
            }

            init() {
                const canvas = document.getElementById('canvas');
                
                // Scene setup
                this.scene = new THREE.Scene();
                this.scene.fog = new THREE.Fog(0x001100, 20, 100);
                
                // Camera setup
                this.camera = new THREE.PerspectiveCamera(
                    75, 
                    window.innerWidth / window.innerHeight, 
                    0.1, 
                    1000
                );
                this.updateCameraPosition();
                
                // Renderer setup
                this.renderer = new THREE.WebGLRenderer({ 
                    canvas: canvas, 
                    antialias: true,
                    alpha: true 
                });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x001100, 1);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

                // Performance optimizations
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                this.renderer.shadowMap.autoUpdate = false; // Manual shadow updates
                this.renderer.info.autoReset = false;
                
                this.setupLighting();
                this.createBarLayers();
                this.createGridFloor();
            }

            setupLighting() {
                // Ambient light
                const ambientLight = new THREE.AmbientLight(0x002200, 0.4);
                this.scene.add(ambientLight);

                // Main directional light
                const directionalLight = new THREE.DirectionalLight(0x00ff00, 0.8);
                directionalLight.position.set(10, 20, 10);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 2048;
                directionalLight.shadow.mapSize.height = 2048;
                this.scene.add(directionalLight);

                // Colored point lights for atmosphere
                const pointLight1 = new THREE.PointLight(0x00ff00, 1, 50);
                pointLight1.position.set(15, 10, 15);
                this.scene.add(pointLight1);

                const pointLight2 = new THREE.PointLight(0x00cc00, 1, 50);
                pointLight2.position.set(-15, 10, -15);
                this.scene.add(pointLight2);

                const pointLight3 = new THREE.PointLight(0x00aa00, 0.8, 40);
                pointLight3.position.set(0, 15, -20);
                this.scene.add(pointLight3);
            }

            createBarLayers() {
                // Create shared geometry for performance
                if (!this.sharedGeometry) {
                    this.sharedGeometry = new THREE.BoxGeometry(0.15, 0.1, 0.15);
                }

                // Create 3 layers of bars
                for (let layer = 0; layer < 3; layer++) {
                    const layerGroup = new THREE.Group();
                    const bars = [];

                    const radius = 8 + (layer * this.layerSpacing);

                    // Create material pool for this layer
                    const layerMaterials = [];

                    for (let i = 0; i < this.barCount; i++) {
                        const angle = (i / this.barCount) * Math.PI * 2;
                        const x = Math.cos(angle) * radius;
                        const z = Math.sin(angle) * radius;

                        // Reuse shared geometry
                        const geometry = this.sharedGeometry;

                        // Create material with hacker green gradients
                        const greenIntensity = 0.3 + (layer * 0.2) + (i / this.barCount) * 0.5;
                        const material = new THREE.MeshPhongMaterial({
                            color: new THREE.Color(0, greenIntensity, 0),
                            transparent: true,
                            opacity: 0.9,
                            shininess: 100,
                            emissive: new THREE.Color(0, greenIntensity * 0.2, 0)
                        });

                        layerMaterials.push(material);

                        const bar = new THREE.Mesh(geometry, material);
                        bar.position.set(x, 0, z);
                        bar.castShadow = true;
                        bar.receiveShadow = true;

                        bars.push({
                            mesh: bar,
                            baseHeight: 0.1,
                            targetHeight: 0.1,
                            currentHeight: 0.1,
                            material: material,
                            baseGreenIntensity: greenIntensity
                        });

                        layerGroup.add(bar);
                    }

                    this.barLayers.push({
                        group: layerGroup,
                        bars: bars,
                        layer: layer,
                        materials: layerMaterials
                    });

                    this.scene.add(layerGroup);
                }
            }

            createGridFloor() {
                // Create reflective grid floor - reduced complexity for performance
                const gridSize = 40;
                const gridDivisions = 25;

                // Main floor plane
                const floorGeometry = new THREE.PlaneGeometry(gridSize, gridSize);
                const floorMaterial = new THREE.MeshPhongMaterial({
                    color: 0x002200,
                    transparent: true,
                    opacity: 0.4,
                    reflectivity: this.gridReflection,
                    shininess: 100
                });

                const floor = new THREE.Mesh(floorGeometry, floorMaterial);
                floor.rotation.x = -Math.PI / 2;
                floor.position.y = -0.1;
                floor.receiveShadow = true;

                // Grid lines
                const gridHelper = new THREE.GridHelper(gridSize, gridDivisions, 0x00ff00, 0x004400);
                gridHelper.position.y = 0;
                gridHelper.material.transparent = true;
                gridHelper.material.opacity = 0.7;

                this.gridFloor = new THREE.Group();
                this.gridFloor.add(floor);
                this.gridFloor.add(gridHelper);
                this.scene.add(this.gridFloor);

                // Create reflection group for bars
                this.createReflections();
            }

            createReflections() {
                this.reflectionGroup = new THREE.Group();
                this.reflectionGroup.scale.y = -1;
                this.reflectionGroup.position.y = -0.1; // Position at floor level

                // Create reflection bars for each layer - optimized
                for (let layerIndex = 0; layerIndex < this.barLayers.length; layerIndex++) {
                    const originalLayer = this.barLayers[layerIndex];
                    const reflectionLayerGroup = new THREE.Group();

                    for (let barIndex = 0; barIndex < originalLayer.bars.length; barIndex++) {
                        const originalBar = originalLayer.bars[barIndex];

                        // Reuse shared geometry instead of cloning
                        const reflectionGeometry = this.sharedGeometry;

                        // Create optimized reflection material
                        const reflectionMaterial = new THREE.MeshPhongMaterial({
                            color: originalBar.mesh.material.color.clone(),
                            transparent: true,
                            opacity: this.gridReflection * 0.4,
                            shininess: 50, // Reduced for performance
                            emissive: originalBar.mesh.material.emissive.clone()
                        });

                        const reflectionBar = new THREE.Mesh(reflectionGeometry, reflectionMaterial);
                        reflectionBar.position.copy(originalBar.mesh.position);
                        reflectionBar.scale.copy(originalBar.mesh.scale);

                        reflectionLayerGroup.add(reflectionBar);
                    }

                    this.reflectionGroup.add(reflectionLayerGroup);
                }

                this.scene.add(this.reflectionGroup);
            }

            setupAudio(file) {
                if (this.audio) {
                    this.audio.pause();
                }

                this.audio = new Audio();
                this.audio.src = URL.createObjectURL(file);
                this.audio.crossOrigin = "anonymous";

                if (!this.audioContext) {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.analyser = this.audioContext.createAnalyser();
                    this.analyser.fftSize = 512;
                    this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
                }

                const source = this.audioContext.createMediaElementSource(this.audio);
                source.connect(this.analyser);
                this.analyser.connect(this.audioContext.destination);

                this.audio.addEventListener('loadeddata', () => {
                    console.log('Audio loaded successfully');
                });
            }

            togglePlayPause() {
                if (!this.audio) return;

                if (this.isPlaying) {
                    this.audio.pause();
                    this.isPlaying = false;
                } else {
                    if (this.audioContext.state === 'suspended') {
                        this.audioContext.resume();
                    }
                    this.audio.play();
                    this.isPlaying = true;
                }
            }

            updateCameraPosition() {
                const x = Math.cos(this.cameraRotationY) * Math.cos(this.cameraRotationX) * this.cameraDistance;
                const y = Math.sin(this.cameraRotationX) * this.cameraDistance;
                const z = Math.sin(this.cameraRotationY) * Math.cos(this.cameraRotationX) * this.cameraDistance;

                this.camera.position.set(x, y, z);
                this.camera.lookAt(0, 2, 0);
            }

            setupEventListeners() {
                // File input
                document.getElementById('fileInput').addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.setupAudio(e.target.files[0]);
                    }
                });

                // Play/Pause button
                document.getElementById('playPause').addEventListener('click', () => {
                    this.togglePlayPause();
                });

                // Control sliders
                document.getElementById('sensitivity').addEventListener('input', (e) => {
                    this.sensitivity = parseFloat(e.target.value);
                    document.getElementById('sensitivityValue').textContent = this.sensitivity.toFixed(1);
                });

                document.getElementById('barCount').addEventListener('input', (e) => {
                    this.barCount = parseInt(e.target.value);
                    document.getElementById('barCountValue').textContent = this.barCount;
                    this.recreateBars();
                });

                document.getElementById('layerSpacing').addEventListener('input', (e) => {
                    this.layerSpacing = parseFloat(e.target.value);
                    document.getElementById('layerSpacingValue').textContent = this.layerSpacing.toFixed(1);
                    this.recreateBars();
                });

                document.getElementById('gridReflection').addEventListener('input', (e) => {
                    this.gridReflection = parseFloat(e.target.value);
                    document.getElementById('gridReflectionValue').textContent = this.gridReflection.toFixed(1);
                    this.updateReflectionOpacity();
                });

                // Mouse controls
                const canvas = document.getElementById('canvas');

                canvas.addEventListener('mousedown', (e) => {
                    this.isMouseDown = true;
                    this.mouseX = e.clientX;
                    this.mouseY = e.clientY;
                });

                canvas.addEventListener('mousemove', (e) => {
                    if (this.isMouseDown) {
                        const deltaX = e.clientX - this.mouseX;
                        const deltaY = e.clientY - this.mouseY;

                        this.cameraRotationY += deltaX * 0.01;
                        this.cameraRotationX -= deltaY * 0.01;
                        this.cameraRotationX = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.cameraRotationX));

                        this.updateCameraPosition();

                        this.mouseX = e.clientX;
                        this.mouseY = e.clientY;
                    }
                });

                canvas.addEventListener('mouseup', () => {
                    this.isMouseDown = false;
                });

                canvas.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    this.cameraDistance += e.deltaY * 0.01;
                    this.cameraDistance = Math.max(5, Math.min(50, this.cameraDistance));
                    this.updateCameraPosition();
                });

                // Window resize
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            recreateBars() {
                // Remove existing bar layers
                this.barLayers.forEach(layer => {
                    this.scene.remove(layer.group);
                });
                this.barLayers = [];

                // Remove existing reflections
                if (this.reflectionGroup) {
                    this.scene.remove(this.reflectionGroup);
                }

                // Recreate bars and reflections
                this.createBarLayers();
                this.createReflections();
            }

            updateReflectionOpacity() {
                if (this.reflectionGroup) {
                    this.reflectionGroup.children.forEach(layerGroup => {
                        layerGroup.children.forEach(bar => {
                            bar.material.opacity = this.gridReflection * 0.5;
                        });
                    });
                }

                // Update floor reflection
                if (this.gridFloor) {
                    this.gridFloor.children[0].material.reflectivity = this.gridReflection;
                }
            }

            updateVisualization() {
                if (!this.analyser || !this.dataArray) return;

                // Skip frames for better performance
                this.frameCount++;
                if (this.frameCount % this.updateFrequency !== 0) return;

                this.analyser.getByteFrequencyData(this.dataArray);

                // Pre-calculate frequency ranges once
                const frequencyRange = Math.floor(this.dataArray.length / 3);

                // Update each layer with different frequency ranges
                for (let layerIndex = 0; layerIndex < this.barLayers.length; layerIndex++) {
                    const layer = this.barLayers[layerIndex];
                    const startIndex = layerIndex * frequencyRange;
                    const endIndex = Math.min(startIndex + frequencyRange, this.dataArray.length);
                    const rangeSize = endIndex - startIndex;

                    // Batch update bars for better performance
                    for (let barIndex = 0; barIndex < layer.bars.length; barIndex++) {
                        const barData = layer.bars[barIndex];

                        // Map bar index to frequency data
                        const dataIndex = Math.floor(startIndex + (barIndex / layer.bars.length) * rangeSize);
                        const frequency = this.dataArray[dataIndex] || 0;
                        const normalizedFreq = frequency * 0.00392156863; // Faster than / 255

                        // Calculate target height with sensitivity
                        barData.targetHeight = barData.baseHeight + (normalizedFreq * this.sensitivity * 5);

                        // Smooth animation with optimized lerp
                        const diff = barData.targetHeight - barData.currentHeight;
                        barData.currentHeight += diff * 0.15;

                        // Update bar scale and position
                        const scaleY = barData.currentHeight / barData.baseHeight;
                        barData.mesh.scale.y = scaleY;
                        barData.mesh.position.y = barData.currentHeight * 0.5;

                        // Update color based on frequency - optimized
                        const greenIntensity = Math.min(1, barData.baseGreenIntensity + (normalizedFreq * 0.5));
                        barData.material.color.g = greenIntensity;
                        barData.material.emissive.g = Math.min(0.3, greenIntensity * 0.3);
                    }
                }

                // Update reflections less frequently for performance
                if (this.frameCount % (this.updateFrequency * 2) === 0 && this.reflectionGroup) {
                    for (let layerIndex = 0; layerIndex < this.barLayers.length; layerIndex++) {
                        const layer = this.barLayers[layerIndex];
                        const reflectionLayer = this.reflectionGroup.children[layerIndex];

                        if (reflectionLayer) {
                            for (let barIndex = 0; barIndex < layer.bars.length; barIndex++) {
                                const barData = layer.bars[barIndex];
                                const reflectionBar = reflectionLayer.children[barIndex];

                                if (reflectionBar) {
                                    // Update scale and position for reflection
                                    reflectionBar.scale.y = barData.mesh.scale.y;
                                    reflectionBar.position.y = -barData.mesh.position.y;

                                    // Update color
                                    reflectionBar.material.color.g = barData.material.color.g;
                                    reflectionBar.material.emissive.g = barData.material.emissive.g;
                                }
                            }
                        }
                    }
                }
            }

            animate() {
                requestAnimationFrame(() => this.animate());

                this.updateVisualization();

                // Subtle rotation of the entire scene - optimized
                if (this.barLayers.length > 0) {
                    const baseRotation = this.frameCount * 0.001;
                    for (let i = 0; i < this.barLayers.length; i++) {
                        this.barLayers[i].group.rotation.y = baseRotation * (i + 1);
                    }

                    if (this.reflectionGroup) {
                        this.reflectionGroup.rotation.y = baseRotation;
                    }
                }

                // Update shadows less frequently
                if (this.frameCount % 10 === 0) {
                    this.renderer.shadowMap.needsUpdate = true;
                }

                this.renderer.render(this.scene, this.camera);

                // Reset frame counter to prevent overflow
                if (this.frameCount > 10000) {
                    this.frameCount = 0;
                }
            }
        }

        // Initialize the visualizer when the page loads
        window.addEventListener('load', () => {
            new MultiLayerBarsVisualizer();
        });
    </script>
</body>
</html>
