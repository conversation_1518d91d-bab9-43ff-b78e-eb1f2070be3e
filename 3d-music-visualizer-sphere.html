<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Modular Transforming Sphere Music Visualizer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e);
            overflow: hidden;
            font-family: 'Arial', sans-serif;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #canvas {
            display: block;
            background: radial-gradient(circle, #000428, #004e92);
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            color: white;
        }

        #fileInput {
            margin: 10px 0;
            padding: 5px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            color: white;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            padding: 8px 15px;
            margin: 5px;
            border-radius: 5px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        #info {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
        <div id="controls">
            <h3>3D Music Visualizer</h3>
            <input type="file" id="fileInput" accept="audio/*">
            <br>
            <button id="playBtn">Play/Pause</button>
            <button id="resetBtn">Reset</button>
            <br>
            <label>Sensitivity: <input type="range" id="sensitivity" min="0.1" max="3" step="0.1" value="1"></label>
        </div>
        <div id="info">
            Click and drag to rotate • Scroll to zoom • Load an audio file to begin
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        class ModularSphereVisualizer {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.sphere = null;
                this.modules = [];
                this.audioContext = null;
                this.analyser = null;
                this.dataArray = null;
                this.audio = null;
                this.isPlaying = false;
                this.mouseX = 0;
                this.mouseY = 0;
                this.targetRotationX = 0;
                this.targetRotationY = 0;
                this.currentRotationX = 0;
                this.currentRotationY = 0;
                this.zoom = 5;
                this.sensitivity = 1;
                
                this.init();
                this.setupEventListeners();
                this.animate();
            }

            init() {
                const canvas = document.getElementById('canvas');
                const container = document.getElementById('container');
                
                // Scene setup
                this.scene = new THREE.Scene();
                this.scene.fog = new THREE.Fog(0x000428, 10, 50);
                
                // Camera setup
                this.camera = new THREE.PerspectiveCamera(
                    75, 
                    window.innerWidth / window.innerHeight, 
                    0.1, 
                    1000
                );
                this.camera.position.z = this.zoom;
                
                // Renderer setup
                this.renderer = new THREE.WebGLRenderer({ 
                    canvas: canvas, 
                    antialias: true,
                    alpha: true 
                });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x000428, 1);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                
                // Lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
                this.scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 10, 5);
                directionalLight.castShadow = true;
                this.scene.add(directionalLight);
                
                const pointLight1 = new THREE.PointLight(0x667eea, 1, 100);
                pointLight1.position.set(10, 0, 10);
                this.scene.add(pointLight1);
                
                const pointLight2 = new THREE.PointLight(0x764ba2, 1, 100);
                pointLight2.position.set(-10, 0, -10);
                this.scene.add(pointLight2);
                
                // Create modular sphere
                this.createModularSphere();
                
                // Particle system
                this.createParticleSystem();
            }

            createModularSphere() {
                const sphereGroup = new THREE.Group();
                
                // Main sphere geometry
                const mainGeometry = new THREE.SphereGeometry(1, 32, 32);
                const mainMaterial = new THREE.MeshPhongMaterial({
                    color: 0x667eea,
                    transparent: true,
                    opacity: 0.7,
                    wireframe: false
                });
                
                const mainSphere = new THREE.Mesh(mainGeometry, mainMaterial);
                sphereGroup.add(mainSphere);
                
                // Create modular components
                const moduleCount = 20;
                for (let i = 0; i < moduleCount; i++) {
                    const module = this.createModule(i, moduleCount);
                    this.modules.push(module);
                    sphereGroup.add(module.mesh);
                }
                
                // Wireframe overlay
                const wireframeGeometry = new THREE.SphereGeometry(1.02, 16, 16);
                const wireframeMaterial = new THREE.MeshBasicMaterial({
                    color: 0x764ba2,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.3
                });
                const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial);
                sphereGroup.add(wireframe);
                
                this.sphere = sphereGroup;
                this.scene.add(this.sphere);
            }

            createModule(index, total) {
                const phi = Math.acos(-1 + (2 * index) / total);
                const theta = Math.sqrt(total * Math.PI) * phi;
                
                const x = Math.cos(theta) * Math.sin(phi);
                const y = Math.sin(theta) * Math.sin(phi);
                const z = Math.cos(phi);
                
                const geometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
                const material = new THREE.MeshPhongMaterial({
                    color: new THREE.Color().setHSL((index / total), 0.8, 0.6),
                    transparent: true,
                    opacity: 0.8
                });
                
                const mesh = new THREE.Mesh(geometry, material);
                mesh.position.set(x * 1.2, y * 1.2, z * 1.2);
                
                return {
                    mesh: mesh,
                    originalPosition: new THREE.Vector3(x * 1.2, y * 1.2, z * 1.2),
                    baseScale: 1,
                    frequency: Math.random() * 10 + 1
                };
            }

            createParticleSystem() {
                const particleCount = 1000;
                const particles = new THREE.BufferGeometry();
                const positions = new Float32Array(particleCount * 3);
                const colors = new Float32Array(particleCount * 3);
                
                for (let i = 0; i < particleCount; i++) {
                    positions[i * 3] = (Math.random() - 0.5) * 20;
                    positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
                    positions[i * 3 + 2] = (Math.random() - 0.5) * 20;
                    
                    const color = new THREE.Color().setHSL(Math.random(), 0.8, 0.6);
                    colors[i * 3] = color.r;
                    colors[i * 3 + 1] = color.g;
                    colors[i * 3 + 2] = color.b;
                }
                
                particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
                
                const particleMaterial = new THREE.PointsMaterial({
                    size: 0.05,
                    vertexColors: true,
                    transparent: true,
                    opacity: 0.6
                });
                
                this.particles = new THREE.Points(particles, particleMaterial);
                this.scene.add(this.particles);
            }

            setupAudio(file) {
                if (this.audio) {
                    this.audio.pause();
                }
                
                this.audio = new Audio();
                this.audio.src = URL.createObjectURL(file);
                
                if (!this.audioContext) {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.analyser = this.audioContext.createAnalyser();
                    this.analyser.fftSize = 256;
                    this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
                }
                
                const source = this.audioContext.createMediaElementSource(this.audio);
                source.connect(this.analyser);
                this.analyser.connect(this.audioContext.destination);
            }

            updateVisualization() {
                if (!this.analyser || !this.dataArray) return;
                
                this.analyser.getByteFrequencyData(this.dataArray);
                
                // Update main sphere
                const avgFrequency = this.dataArray.reduce((a, b) => a + b) / this.dataArray.length;
                const normalizedAvg = (avgFrequency / 255) * this.sensitivity;
                
                if (this.sphere) {
                    this.sphere.scale.setScalar(1 + normalizedAvg * 0.3);
                    this.sphere.rotation.y += normalizedAvg * 0.02;
                }
                
                // Update modules
                this.modules.forEach((module, index) => {
                    const frequencyIndex = Math.floor((index / this.modules.length) * this.dataArray.length);
                    const frequency = this.dataArray[frequencyIndex] / 255;
                    
                    // Scale based on frequency
                    const scale = 1 + frequency * this.sensitivity * 2;
                    module.mesh.scale.setScalar(scale);
                    
                    // Color based on frequency
                    const hue = (frequency + index / this.modules.length) % 1;
                    module.mesh.material.color.setHSL(hue, 0.8, 0.6);
                    
                    // Position transformation
                    const distance = 1.2 + frequency * this.sensitivity * 0.5;
                    const direction = module.originalPosition.clone().normalize();
                    module.mesh.position.copy(direction.multiplyScalar(distance));
                    
                    // Rotation
                    module.mesh.rotation.x += frequency * 0.1;
                    module.mesh.rotation.y += frequency * 0.1;
                });
                
                // Update particles
                if (this.particles) {
                    const positions = this.particles.geometry.attributes.position.array;
                    for (let i = 0; i < positions.length; i += 3) {
                        const frequencyIndex = Math.floor((i / positions.length) * this.dataArray.length);
                        const frequency = this.dataArray[frequencyIndex] / 255;
                        
                        positions[i + 1] += Math.sin(Date.now() * 0.001 + i) * frequency * 0.1;
                    }
                    this.particles.geometry.attributes.position.needsUpdate = true;
                    this.particles.rotation.y += normalizedAvg * 0.01;
                }
            }

            setupEventListeners() {
                // File input
                document.getElementById('fileInput').addEventListener('change', (e) => {
                    if (e.target.files[0]) {
                        this.setupAudio(e.target.files[0]);
                    }
                });
                
                // Play/Pause button
                document.getElementById('playBtn').addEventListener('click', () => {
                    if (this.audio) {
                        if (this.isPlaying) {
                            this.audio.pause();
                        } else {
                            this.audio.play();
                        }
                        this.isPlaying = !this.isPlaying;
                    }
                });
                
                // Reset button
                document.getElementById('resetBtn').addEventListener('click', () => {
                    if (this.audio) {
                        this.audio.currentTime = 0;
                    }
                    this.currentRotationX = 0;
                    this.currentRotationY = 0;
                    this.targetRotationX = 0;
                    this.targetRotationY = 0;
                });
                
                // Sensitivity slider
                document.getElementById('sensitivity').addEventListener('input', (e) => {
                    this.sensitivity = parseFloat(e.target.value);
                });
                
                // Mouse controls
                document.addEventListener('mousemove', (e) => {
                    this.mouseX = (e.clientX / window.innerWidth) * 2 - 1;
                    this.mouseY = -(e.clientY / window.innerHeight) * 2 + 1;
                    
                    this.targetRotationX = this.mouseY * Math.PI;
                    this.targetRotationY = this.mouseX * Math.PI;
                });
                
                // Scroll zoom
                document.addEventListener('wheel', (e) => {
                    this.zoom += e.deltaY * 0.01;
                    this.zoom = Math.max(2, Math.min(20, this.zoom));
                    this.camera.position.z = this.zoom;
                });
                
                // Window resize
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            animate() {
                requestAnimationFrame(() => this.animate());
                
                // Smooth camera rotation
                this.currentRotationX += (this.targetRotationX - this.currentRotationX) * 0.05;
                this.currentRotationY += (this.targetRotationY - this.currentRotationY) * 0.05;
                
                if (this.sphere) {
                    this.sphere.rotation.x = this.currentRotationX * 0.3;
                    this.sphere.rotation.y += 0.005 + this.currentRotationY * 0.001;
                }
                
                // Update visualization based on audio
                this.updateVisualization();
                
                // Render
                this.renderer.render(this.scene, this.camera);
            }
        }

        // Initialize the visualizer when the page loads
        window.addEventListener('load', () => {
            new ModularSphereVisualizer();
        });
    </script>
</body>
</html>
