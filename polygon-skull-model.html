<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Polygon Skull Model</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
            overflow: hidden;
            font-family: 'Courier New', monospace;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #00ff88;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff88;
        }
        
        #controls {
            position: absolute;
            top: 20px;
            right: 20px;
            color: #00ff88;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ff88;
        }
        
        button {
            background: #1a1a2e;
            color: #00ff88;
            border: 1px solid #00ff88;
            padding: 8px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
        }
        
        button:hover {
            background: #00ff88;
            color: #1a1a2e;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #00ff88;
            font-size: 24px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">Loading 3D Skull Model...</div>
        <div id="info">
            <h3>3D Polygon Skull Model</h3>
            <p>Mouse: Rotate camera</p>
            <p>Scroll: Zoom in/out</p>
            <p>Polygons: <span id="polyCount">0</span></p>
            <p>Vertices: <span id="vertCount">0</span></p>
        </div>
        <div id="controls">
            <button onclick="toggleWireframe()">Toggle Wireframe</button>
            <button onclick="toggleAnimation()">Toggle Animation</button>
            <button onclick="changeModel()">Change Model</button>
            <button onclick="toggleLighting()">Toggle Lighting</button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    
    <script>
        let scene, camera, renderer, controls;
        let skullMesh, currentModel = 0;
        let isAnimating = true;
        let isWireframe = false;
        let lightingEnabled = true;
        let ambientLight, directionalLight, pointLight;
        
        const models = ['skull', 'pyramid', 'crystal', 'abstract'];
        
        function init() {
            // Scene setup
            scene = new THREE.Scene();
            scene.fog = new THREE.Fog(0x0a0a0a, 10, 50);
            
            // Camera setup
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 2, 8);
            
            // Renderer setup
            renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x0a0a0a, 1);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.getElementById('container').appendChild(renderer.domElement);
            
            // Controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.maxDistance = 20;
            controls.minDistance = 2;
            
            // Lighting
            setupLighting();
            
            // Create skull model
            createSkullModel();
            
            // Add particle system
            createParticleSystem();
            
            // Hide loading
            document.getElementById('loading').style.display = 'none';
            
            // Start animation
            animate();
        }
        
        function setupLighting() {
            // Ambient light
            ambientLight = new THREE.AmbientLight(0x404040, 0.3);
            scene.add(ambientLight);
            
            // Directional light
            directionalLight = new THREE.DirectionalLight(0x00ff88, 0.8);
            directionalLight.position.set(5, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
            
            // Point light
            pointLight = new THREE.PointLight(0xff4444, 0.5, 20);
            pointLight.position.set(-5, 5, -5);
            scene.add(pointLight);
        }
        
        function createSkullModel() {
            const geometry = new THREE.BufferGeometry();
            
            // Define skull vertices (simplified skull shape)
            const vertices = new Float32Array([
                // Front face vertices
                0, 2, 1,     // top center
                -1, 1, 1,    // top left
                1, 1, 1,     // top right
                -1.5, 0, 1,  // middle left
                1.5, 0, 1,   // middle right
                -1, -1, 1,   // bottom left
                1, -1, 1,    // bottom right
                0, -2, 1,    // bottom center
                
                // Back face vertices
                0, 2, -1,    // top center back
                -1, 1, -1,   // top left back
                1, 1, -1,    // top right back
                -1.5, 0, -1, // middle left back
                1.5, 0, -1,  // middle right back
                -1, -1, -1,  // bottom left back
                1, -1, -1,   // bottom right back
                0, -2, -1,   // bottom center back
                
                // Eye socket vertices
                -0.5, 0.5, 1.1,  // left eye
                0.5, 0.5, 1.1,   // right eye
                
                // Nasal cavity
                0, 0, 1.1,       // nose center
                -0.2, -0.3, 1.1, // nose left
                0.2, -0.3, 1.1,  // nose right
                
                // Jaw vertices
                -0.8, -1.5, 0.8, // jaw left
                0.8, -1.5, 0.8,  // jaw right
                0, -1.8, 0.5,    // jaw center
            ]);
            
            // Define faces (triangles)
            const indices = new Uint16Array([
                // Front face triangles
                0, 1, 2,   // top triangle
                1, 3, 4,   // upper middle
                1, 4, 2,   // upper middle right
                3, 5, 6,   // lower middle
                3, 6, 4,   // lower middle right
                5, 7, 6,   // bottom triangle
                
                // Back face triangles
                8, 10, 9,  // top triangle back
                9, 12, 11, // upper middle back
                9, 10, 12, // upper middle right back
                11, 14, 13, // lower middle back
                11, 12, 14, // lower middle right back
                13, 14, 15, // bottom triangle back
                
                // Side connections
                0, 8, 1,   // top left connection
                1, 8, 9,   // top left back
                0, 2, 8,   // top right connection
                2, 10, 8,  // top right back
                
                // Eye sockets
                1, 16, 3,  // left eye socket
                2, 4, 17,  // right eye socket
                
                // Nasal cavity
                18, 19, 20, // nose triangle
                
                // Jaw connections
                5, 22, 7,  // left jaw
                6, 7, 23,  // right jaw
                7, 22, 24, // jaw center left
                7, 24, 23, // jaw center right
            ]);
            
            geometry.setIndex(indices);
            geometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));
            geometry.computeVertexNormals();
            
            // Create material
            const material = new THREE.MeshPhongMaterial({
                color: 0xcccccc,
                shininess: 30,
                transparent: true,
                opacity: 0.9,
                side: THREE.DoubleSide
            });
            
            // Create mesh
            if (skullMesh) {
                scene.remove(skullMesh);
            }
            
            skullMesh = new THREE.Mesh(geometry, material);
            skullMesh.castShadow = true;
            skullMesh.receiveShadow = true;
            scene.add(skullMesh);
            
            // Update info
            updateInfo(geometry);
        }
        
        function createPyramidModel() {
            const geometry = new THREE.ConeGeometry(2, 3, 4);
            const material = new THREE.MeshPhongMaterial({
                color: 0x00ff88,
                shininess: 100,
                transparent: true,
                opacity: 0.8
            });
            
            if (skullMesh) {
                scene.remove(skullMesh);
            }
            
            skullMesh = new THREE.Mesh(geometry, material);
            skullMesh.castShadow = true;
            skullMesh.receiveShadow = true;
            scene.add(skullMesh);
            
            updateInfo(geometry);
        }
        
        function createCrystalModel() {
            const geometry = new THREE.OctahedronGeometry(2);
            const material = new THREE.MeshPhongMaterial({
                color: 0x4444ff,
                shininess: 100,
                transparent: true,
                opacity: 0.7
            });
            
            if (skullMesh) {
                scene.remove(skullMesh);
            }
            
            skullMesh = new THREE.Mesh(geometry, material);
            skullMesh.castShadow = true;
            skullMesh.receiveShadow = true;
            scene.add(skullMesh);
            
            updateInfo(geometry);
        }
        
        function createAbstractModel() {
            const geometry = new THREE.IcosahedronGeometry(2, 1);
            const material = new THREE.MeshPhongMaterial({
                color: 0xff4444,
                shininess: 50,
                transparent: true,
                opacity: 0.8,
                wireframe: false
            });
            
            if (skullMesh) {
                scene.remove(skullMesh);
            }
            
            skullMesh = new THREE.Mesh(geometry, material);
            skullMesh.castShadow = true;
            skullMesh.receiveShadow = true;
            scene.add(skullMesh);
            
            updateInfo(geometry);
        }
        
        function createParticleSystem() {
            const particleCount = 1000;
            const particles = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);
            
            for (let i = 0; i < particleCount * 3; i += 3) {
                positions[i] = (Math.random() - 0.5) * 50;
                positions[i + 1] = (Math.random() - 0.5) * 50;
                positions[i + 2] = (Math.random() - 0.5) * 50;
            }
            
            particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            
            const particleMaterial = new THREE.PointsMaterial({
                color: 0x00ff88,
                size: 0.1,
                transparent: true,
                opacity: 0.6
            });
            
            const particleSystem = new THREE.Points(particles, particleMaterial);
            scene.add(particleSystem);
        }
        
        function updateInfo(geometry) {
            const positions = geometry.attributes.position;
            const indices = geometry.index;
            
            const vertexCount = positions ? positions.count : 0;
            const faceCount = indices ? indices.count / 3 : 0;
            
            document.getElementById('polyCount').textContent = Math.floor(faceCount);
            document.getElementById('vertCount').textContent = vertexCount;
        }
        
        function toggleWireframe() {
            isWireframe = !isWireframe;
            if (skullMesh) {
                skullMesh.material.wireframe = isWireframe;
            }
        }
        
        function toggleAnimation() {
            isAnimating = !isAnimating;
        }
        
        function changeModel() {
            currentModel = (currentModel + 1) % models.length;
            
            switch (models[currentModel]) {
                case 'skull':
                    createSkullModel();
                    break;
                case 'pyramid':
                    createPyramidModel();
                    break;
                case 'crystal':
                    createCrystalModel();
                    break;
                case 'abstract':
                    createAbstractModel();
                    break;
            }
        }
        
        function toggleLighting() {
            lightingEnabled = !lightingEnabled;
            ambientLight.visible = lightingEnabled;
            directionalLight.visible = lightingEnabled;
            pointLight.visible = lightingEnabled;
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            if (isAnimating && skullMesh) {
                skullMesh.rotation.y += 0.01;
                skullMesh.rotation.x += 0.005;
                
                // Floating animation
                skullMesh.position.y = Math.sin(Date.now() * 0.001) * 0.5;
            }
            
            // Animate lights
            if (pointLight) {
                pointLight.position.x = Math.sin(Date.now() * 0.002) * 8;
                pointLight.position.z = Math.cos(Date.now() * 0.002) * 8;
            }
            
            controls.update();
            renderer.render(scene, camera);
        }
        
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        
        window.addEventListener('resize', onWindowResize);
        
        // Initialize the scene
        init();
    </script>
</body>
</html>
