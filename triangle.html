<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Music Reactive Subdividing Triangle</title>
<style>
  body {
    margin: 0;
    background: black;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  canvas {
    display: block;
  }
  #controls {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0,0,0,0.5);
    padding: 10px;
    border-radius: 8px;
  }
  label {
    color: white;
    font-family: sans-serif;
  }
</style>
</head>
<body>
<div id="controls">
  <label>
    Upload Music:
    <input type="file" id="fileInput" accept="audio/*">
  </label>
  <br>
  <button id="playPause">Play/Pause</button>
</div>
<canvas id="canvas"></canvas>

<script>
const canvas = document.getElementById("canvas");
const ctx = canvas.getContext("2d");
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;

let audioCtx, analyser, source, dataArray, audioBufferSource;
let isPlaying = false;
let triangles = [];
let maxDepth = 5;

class Triangle {
  constructor(p1, p2, p3, depth=0) {
    this.p1 = p1;
    this.p2 = p2;
    this.p3 = p3;
    this.depth = depth;
    this.children = [];
  }

  draw() {
    ctx.beginPath();
    ctx.moveTo(this.p1.x, this.p1.y);
    ctx.lineTo(this.p2.x, this.p2.y);
    ctx.lineTo(this.p3.x, this.p3.y);
    ctx.closePath();
    ctx.strokeStyle = `hsl(${this.depth*60}, 100%, 50%)`;
    ctx.stroke();
  }

  subdivide() {
    if (this.depth >= maxDepth || this.children.length > 0) return;

    let m1 = midpoint(this.p1, this.p2);
    let m2 = midpoint(this.p2, this.p3);
    let m3 = midpoint(this.p3, this.p1);

    this.children.push(
      new Triangle(this.p1, m1, m3, this.depth+1),
      new Triangle(this.p2, m1, m2, this.depth+1),
      new Triangle(this.p3, m2, m3, this.depth+1),
      new Triangle(m1, m2, m3, this.depth+1)
    );
  }

  render() {
    if (this.children.length > 0) {
      this.children.forEach(child => child.render());
    } else {
      this.draw();
    }
  }
}

function midpoint(a, b) {
  return {x:(a.x+b.x)/2, y:(a.y+b.y)/2};
}

function setupTriangle() {
  const size = Math.min(canvas.width, canvas.height) * 0.8;
  const h = size * Math.sqrt(3)/2;
  const p1 = {x: canvas.width/2, y: canvas.height/2 - h/2};
  const p2 = {x: canvas.width/2 - size/2, y: canvas.height/2 + h/2};
  const p3 = {x: canvas.width/2 + size/2, y: canvas.height/2 + h/2};
  triangles = [new Triangle(p1,p2,p3,0)];
}

function animate() {
  requestAnimationFrame(animate);
  ctx.clearRect(0,0,canvas.width,canvas.height);

  let volume = getVolume();

  if (volume > 100) { 
    triangles.forEach(t => t.subdivide());
  }

  triangles.forEach(t => t.render());
}

function getVolume() {
  if (!analyser) return 0;
  analyser.getByteFrequencyData(dataArray);
  let sum = dataArray.reduce((a,b)=>a+b,0);
  return sum / dataArray.length;
}

document.getElementById("fileInput").addEventListener("change", async (e)=>{
  const file = e.target.files[0];
  if (!file) return;
  
  if (!audioCtx) audioCtx = new (window.AudioContext || window.webkitAudioContext)();
  const arrayBuffer = await file.arrayBuffer();
  const audioBuffer = await audioCtx.decodeAudioData(arrayBuffer);

  if (audioBufferSource) audioBufferSource.disconnect();
  audioBufferSource = audioCtx.createBufferSource();
  audioBufferSource.buffer = audioBuffer;

  analyser = audioCtx.createAnalyser();
  analyser.fftSize = 256;
  dataArray = new Uint8Array(analyser.frequencyBinCount);

  audioBufferSource.connect(analyser);
  analyser.connect(audioCtx.destination);
  audioBufferSource.start();
  isPlaying = true;
});

document.getElementById("playPause").addEventListener("click", ()=>{
  if (!audioCtx) return;
  if (isPlaying) {
    audioCtx.suspend();
  } else {
    audioCtx.resume();
  }
  isPlaying = !isPlaying;
});

setupTriangle();
animate();

window.addEventListener("resize", ()=>{
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  setupTriangle();
});
</script>
</body>
</html>
