<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Multi-Layer Audio Bars</title>
<style>
  body { margin: 0; overflow: hidden; background: #000; }
</style>
</head>
<body>
<input type="file" id="audioFile" accept="audio/*" style="position:absolute; z-index:10;">
<script src="https://cdn.jsdelivr.net/npm/three@0.161.0/build/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.161.0/examples/js/controls/OrbitControls.js"></script>

<script>
let scene, camera, renderer, controls, analyser, dataArray, audio, audioCtx;
let barsLayers = [];
const layerColors = [0xff5555, 0x55ff55, 0x5555ff];

init();
animate();

function init() {
    scene = new THREE.Scene();

    camera = new THREE.PerspectiveCamera(60, window.innerWidth/window.innerHeight, 0.1, 500);
    camera.position.set(50, 50, 70);

    renderer = new THREE.WebGLRenderer({antialias:true});
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.body.appendChild(renderer.domElement);

    controls = new THREE.OrbitControls(camera, renderer.domElement);

    // Grid floor
    const size = 200, divisions = 40;
    const grid = new THREE.GridHelper(size, divisions, 0xffffff, 0x555555);
    scene.add(grid);

    // Lights
    const ambient = new THREE.AmbientLight(0xffffff, 0.8);
    scene.add(ambient);
    const dirLight = new THREE.DirectionalLight(0xffffff, 1);
    dirLight.position.set(50, 100, 50);
    scene.add(dirLight);

    // Layers of bars
    const layers = 3;
    for(let l=0; l<layers; l++){
        const bars = [];
        const numBars = 32 + l*16;
        const spacing = 2 + l*1;
        for(let i=0; i<numBars; i++){
            const barGeom = new THREE.BoxGeometry(1.5,1,1.5);
            const barMat = new THREE.MeshStandardMaterial({color: layerColors[l]});
            const bar = new THREE.Mesh(barGeom, barMat);
            bar.position.set((i - numBars/2) * spacing, 0.5, -l*5);
            scene.add(bar);
            bars.push(bar);
        }
        barsLayers.push(bars);
    }

    // Audio setup
    audio = new Audio();
    const fileInput = document.getElementById('audioFile');
    fileInput.addEventListener('change', (e)=>{
        const file = e.target.files[0];
        if(file){
            audio.src = URL.createObjectURL(file);
            audio.load();
            audio.play();
            setupAudio();
        }
    });

    window.addEventListener('resize', onWindowResize, false);
}

function setupAudio(){
    audioCtx = new (window.AudioContext || window.webkitAudioContext)();
    const source = audioCtx.createMediaElementSource(audio);
    analyser = audioCtx.createAnalyser();
    analyser.fftSize = 128;
    source.connect(analyser);
    analyser.connect(audioCtx.destination);
    dataArray = new Uint8Array(analyser.frequencyBinCount);
}

function onWindowResize(){
    camera.aspect = window.innerWidth/window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

function animate(){
    requestAnimationFrame(animate);

    if(analyser){
        analyser.getByteFrequencyData(dataArray);
        barsLayers.forEach((bars)=>{
            const step = Math.floor(dataArray.length / bars.length);
            bars.forEach((bar, i)=>{
                const val = dataArray[i*step] / 10 + 1; // scale up
                bar.scale.y = val;
                bar.position.y = val/2;
            });
        });
    }

    controls.update();
    renderer.render(scene, camera);
}
</script>
</body>
</html>
