<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Spline Speaker Visualizer</title>
<style>
  html,body{margin:0;height:100%;background:#000;overflow:hidden}
  canvas{width:100%;height:100%;display:block}
  #file,#micBtn{position:fixed;top:10px;z-index:10;color:white}
  #micBtn{left:150px}
</style>
</head>
<body>
<canvas id="c"></canvas>
<input id="file" type="file" accept="audio/*">
<button id="micBtn">Use Mic</button>
<script>
const canvas=document.getElementById("c"),ctx=canvas.getContext("2d");
let audioCtx,analyser,dataArray,sourceNode,micStream;
let hue=0;

function resize(){canvas.width=innerWidth;canvas.height=innerHeight}
addEventListener("resize",resize);resize();

// audio setup
function makeAnalyser(){
  audioCtx=new (window.AudioContext||window.webkitAudioContext)();
  analyser=audioCtx.createAnalyser();
  analyser.fftSize=1024;
  dataArray=new Uint8Array(analyser.frequencyBinCount);
}
function connect(node){node.connect(analyser); analyser.connect(audioCtx.destination)}

// file input
file.onchange=e=>{
  if(!file.files[0])return;
  stopAll();
  makeAnalyser();
  const audio=new Audio(URL.createObjectURL(file.files[0]));
  audio.loop=true; audio.crossOrigin="anonymous";
  sourceNode=audioCtx.createMediaElementSource(audio);
  connect(sourceNode); audio.play();
  draw();
};

// mic input
micBtn.onclick=async()=>{
  stopAll();
  micStream=await navigator.mediaDevices.getUserMedia({audio:true});
  makeAnalyser();
  sourceNode=audioCtx.createMediaStreamSource(micStream);
  connect(sourceNode); draw();
};

function stopAll(){
  if(audioCtx) audioCtx.close();
  if(micStream) micStream.getTracks().forEach(t=>t.stop());
}

// draw loop
function draw(){
  requestAnimationFrame(draw);
  if(!analyser)return;
  analyser.getByteFrequencyData(dataArray);
  const w=canvas.width,h=canvas.height,cx=w/2,cy=h/2;
  ctx.fillStyle="rgba(0,0,0,0.2)";
  ctx.fillRect(0,0,w,h);

  // loudness
  let avg=0; for(let v of dataArray) avg+=v; avg/=dataArray.length;
  const amp=avg/255; 
  hue=(hue+1)%360;

  // draw concentric spline speakers
  const rings=5;
  for(let r=1;r<=rings;r++){
    const radius=80*r+amp*80;
    drawSplineCircle(cx,cy,radius,amp*50,`hsl(${hue+r*30},100%,60%)`);
  }
}

// draw circle using splines
function drawSplineCircle(cx,cy,rad,offset,color){
  ctx.beginPath();
  const points=32; // spline resolution
  let angleStep=Math.PI*2/points;
  for(let i=0;i<=points;i++){
    let angle=i*angleStep;
    // audio-driven radial variation
    const val=dataArray[(i*4)%dataArray.length]/255;
    let r=rad+Math.sin(angle*4+Date.now()*0.002)*offset*val;
    let x=cx+Math.cos(angle)*r;
    let y=cy+Math.sin(angle)*r;
    if(i===0) ctx.moveTo(x,y);
    else{
      // quadratic curve to smoothen
      let prevAngle=(i-0.5)*angleStep;
      let midX=cx+Math.cos(prevAngle)*r;
      let midY=cy+Math.sin(prevAngle)*r;
      ctx.quadraticCurveTo(midX,midY,x,y);
    }
  }
  ctx.strokeStyle=color;
  ctx.lineWidth=1.5;
  ctx.shadowBlur=20;
  ctx.shadowColor=color;
  ctx.stroke();
}
</script>
</body>
</html>
