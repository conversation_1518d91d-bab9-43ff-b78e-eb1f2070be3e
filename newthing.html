<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>3D Shader Music Visualizer — Eye Candy</title>
<style>
  :root{
    --bg:#05060a;
    --ui-bg: rgba(8,12,18,0.6);
    --accent: #66d9ef;
    --muted: #9fb6cc;
  }
  html,body{height:100%;margin:0;background:linear-gradient(180deg,#03040a 0%, #071025 100%);font-family:Inter,system-ui,Segoe UI,Roboto,Arial;color:#dfefff;}
  #ui{
    position:fixed; left:12px; top:12px; z-index:30;
    background:var(--ui-bg); padding:10px; border-radius:10px; backdrop-filter: blur(6px);
    box-shadow:0 6px 30px rgba(0,0,0,0.6); border:1px solid rgba(255,255,255,0.04);
    display:flex; gap:8px; align-items:center; flex-wrap:wrap; max-width:calc(100% - 28px);
  }
  button,input[type="range"],label[for=file]{ cursor:pointer; border:0; background:transparent; color:inherit; padding:6px 10px; border-radius:8px; font-weight:600; }
  button:hover,input[type="range"]:hover,label[for=file]:hover{ background:rgba(255,255,255,0.02); }
  #info{ font-size:12px; color:var(--muted); margin-left:8px; }
  canvas{display:block; width:100vw; height:100vh;}
  .small{font-size:12px;color:var(--muted);}
  .ctrl{display:flex; gap:6px; align-items:center;}
  .pill{background:rgba(255,255,255,0.03); padding:6px 8px; border-radius:999px;}
</style>
</head>
<body>
  <div id="ui">
    <label class="pill" for="file">📂 Load audio</label>
    <input id="file" type="file" accept="audio/*" style="display:none">
    <button id="mic">🎤 Mic</button>
    <button id="play">▶ Play</button>
    <div class="ctrl small">Vol <input id="vol" type="range" min="0" max="1" step="0.01" value="0.9"></div>
    <div class="ctrl small">Sens <input id="sens" type="range" min="0.2" max="4" step="0.01" value="1.5"></div>
    <div class="ctrl small">Particles <input id="count" type="range" min="2000" max="20000" step="500" value="8000"></div>
    <div id="info">Tip: upload a track or click mic. Drag to orbit camera.</div>
  </div>

  <canvas id="c"></canvas>

<script type="module">
/*
  3D Shader Music Visualizer
  - Three.js (module) from CDN
  - GLSL shaders for fluid/particle and main deforming mesh
  - Audio input from file or mic; AnalyserNode feeds shader uniforms
  - Controls: file, mic, play/pause, volume, sensitivity, particle count
*/

import * as THREE from 'https://unpkg.com/three@0.152.2/build/three.module.js';
import { OrbitControls } from 'https://unpkg.com/three@0.152.2/examples/jsm/controls/OrbitControls.js';

const canvas = document.getElementById('c');
const renderer = new THREE.WebGLRenderer({ canvas, antialias: true, alpha: true });
renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1.5, 2));
renderer.setSize(innerWidth, innerHeight, false);
renderer.outputEncoding = THREE.sRGBEncoding;

const scene = new THREE.Scene();
scene.fog = new THREE.FogExp2(0x03040a, 0.0022);

const camera = new THREE.PerspectiveCamera(45, innerWidth / innerHeight, 0.1, 5000);
camera.position.set(0, 0.6, 6);

const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;
controls.dampingFactor = 0.06;
controls.minDistance = 2.2;
controls.maxDistance = 20;
controls.autoRotate = false;

// === AUDIO SETUP ===
let audioCtx = null;
let analyser = null;
let dataArray = null;
let sourceNode = null;
let gainNode = null;
let audioElement = null;
let usingMic = false;
let isPlaying = false;

async function ensureAudio() {
  if (audioCtx) return;
  audioCtx = new (window.AudioContext || window.webkitAudioContext)();
  analyser = audioCtx.createAnalyser();
  analyser.fftSize = 2048;
  analyser.smoothingTimeConstant = 0.85;
  const bufferLen = analyser.frequencyBinCount;
  dataArray = new Uint8Array(bufferLen);
  gainNode = audioCtx.createGain();
  gainNode.gain.value = parseFloat(vol.value);
  gainNode.connect(analyser);
  analyser.connect(audioCtx.destination);
}

// UI elements
const fileInput = document.getElementById('file');
const micBtn = document.getElementById('mic');
const playBtn = document.getElementById('play');
const vol = document.getElementById('vol');
const sens = document.getElementById('sens');
const countSlider = document.getElementById('count');

vol.addEventListener('input', () => {
  if (gainNode) gainNode.gain.value = parseFloat(vol.value);
});

// drag & drop
window.addEventListener('dragover', e => { e.preventDefault(); }, false);
window.addEventListener('drop', async e => {
  e.preventDefault();
  if (e.dataTransfer.files.length) {
    await handleFile(e.dataTransfer.files[0]);
  }
}, false);

fileInput.addEventListener('change', async (e) => {
  const f = e.target.files?.[0];
  if (f) await handleFile(f);
});

async function handleFile(f) {
  await ensureAudio();
  usingMic = false;
  if (audioElement) {
    audioElement.pause();
    try { sourceNode.disconnect(); } catch {}
  }
  audioElement = new Audio();
  audioElement.src = URL.createObjectURL(f);
  audioElement.crossOrigin = 'anonymous';
  audioElement.loop = true;
  const elSource = audioCtx.createMediaElementSource(audioElement);
  elSource.connect(gainNode);
  sourceNode = elSource;
  try {
    await audioElement.play();
    isPlaying = true;
    playBtn.textContent = '⏸ Pause';
  } catch (err) {
    // user gesture required on some browsers
    isPlaying = false;
    playBtn.textContent = '▶ Play';
  }
}

micBtn.addEventListener('click', async () => {
  await ensureAudio();
  if (sourceNode) try { sourceNode.disconnect(); } catch {}
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
    const micSrc = audioCtx.createMediaStreamSource(stream);
    micSrc.connect(gainNode);
    sourceNode = micSrc;
    usingMic = true;
    isPlaying = true;
    playBtn.textContent = '⏸ Pause';
  } catch (err) {
    alert('Microphone access denied or unavailable.');
  }
});

playBtn.addEventListener('click', async () => {
  await ensureAudio();
  if (!audioElement && !usingMic) {
    // kick context to allow visuals but no sound
    try { await audioCtx.resume(); } catch {}
    isPlaying = !isPlaying;
    playBtn.textContent = isPlaying ? '⏸ Pause' : '▶ Play';
    return;
  }
  if (audioElement && !usingMic) {
    if (audioCtx.state === 'suspended') await audioCtx.resume();
    if (audioElement.paused) {
      await audioElement.play();
      isPlaying = true;
      playBtn.textContent = '⏸ Pause';
    } else {
      audioElement.pause();
      isPlaying = false;
      playBtn.textContent = '▶ Play';
    }
  } else if (usingMic) {
    // toggle gain mute
    if (gainNode) {
      const cur = gainNode.gain.value;
      if (cur > 0) { gainNode.gain.value = 0; playBtn.textContent = '▶ Play'; }
      else { gainNode.gain.value = parseFloat(vol.value); playBtn.textContent = '⏸ Pause'; }
    }
  }
});

// === SCENE CONTENT ===

// background full-screen plane with moving "liquid" shader
const bgGeo = new THREE.PlaneGeometry(2, 2);
const bgMat = new THREE.ShaderMaterial({
  uniforms: {
    time: { value: 0 },
    resolution: { value: new THREE.Vector2(innerWidth, innerHeight) },
    audio: { value: 0.0 }
  },
  vertexShader: `precision highp float;
    varying vec2 vUv;
    void main(){ vUv = uv; gl_Position = vec4(position, 1.0); }`,
  fragmentShader: `precision highp float;
    varying vec2 vUv;
    uniform float time;
    uniform vec2 resolution;
    uniform float audio;
    // 2D simplex-ish noise (cheap)
    float hash(vec2 p){ return fract(sin(dot(p,vec2(127.1,311.7)))*43758.5453123); }
    float noise(vec2 p){
      vec2 i = floor(p); vec2 f = fract(p);
      float a = hash(i); float b = hash(i + vec2(1.0,0.0));
      float c = hash(i + vec2(0.0,1.0)); float d = hash(i + vec2(1.0,1.0));
      vec2 u = f*f*(3.0-2.0*f);
      return mix(a,b,u.x) + (c-a)*u.y*(1.0-u.x) + (d-b)*u.x*u.y;
    }
    void main(){
      vec2 uv = vUv;
      vec2 p = uv*vec2(resolution.x/resolution.y,1.0);
      float t = time*0.12;
      float n = 0.0;
      float freq = 0.0;
      // layered noise to emulate flow
      n += 0.5*noise(p*1.2 + t*0.6);
      n += 0.35*noise(p*2.3 - t*0.85);
      n += 0.2*noise(p*4.2 + vec2(t*0.6, -t*0.4));
      float flow = smoothstep(0.2, 0.8, n + 0.25*sin(t*0.8 + p.x*3.0));
      // color palette influenced by audio
      vec3 col = mix(vec3(0.02,0.03,0.08), vec3(0.05,0.12,0.2), flow);
      // audio-driven glow
      col += 0.6*vec3(0.12,0.42,0.6) * pow(clamp(audio,0.0,1.0), 1.2) * (0.4 + 0.6*flow);
      // add streaks
      float streak = smoothstep(0.7,1.0, fract(uv.x*6.0 + n*2.0 - t*0.2));
      col += 0.15*vec3(1.0,0.45,0.85) * streak * audio;
      // vignette
      float v = smoothstep(0.9,0.2, length(uv-0.5));
      col *= mix(1.0, 0.6, v);
      gl_FragColor = vec4(pow(col, vec3(0.9)), 1.0);
    }`,
  depthWrite: false,
  depthTest: false,
  transparent: false
});
const bgMesh = new THREE.Mesh(bgGeo, bgMat);
bgMesh.frustumCulled = false;
scene.add(bgMesh);

// central deforming mesh (icosahedron -> shader displacement + rim)
const geom = new THREE.IcosahedronGeometry(1.6, 64); // high tessellation for displacement
const mainMat = new THREE.ShaderMaterial({
  uniforms: {
    time: { value: 0 },
    audioBass: { value: 0.0 },
    audioMid: { value: 0.0 },
    audioHigh: { value: 0.0 },
    colorA: { value: new THREE.Color(0x00e4ff) },
    colorB: { value: new THREE.Color(0xff6abf) }
  },
  vertexShader: `precision highp float;
    uniform float time;
    uniform float audioBass;
    uniform float audioMid;
    varying vec3 vNormal;
    varying vec3 vPos;
    varying float vNoise;
    // simple pseudo noise
    float hash(float n){ return fract(sin(n)*43758.5453); }
    float noise(vec3 x){
      vec3 p = floor(x);
      vec3 f = fract(x);
      f = f*f*(3.0-2.0*f);
      float n = p.x + p.y*57.0 + 113.0*p.z;
      float res = mix(mix(mix(hash(n+0.0), hash(n+1.0), f.x),
                          mix(hash(n+57.0), hash(n+58.0), f.x), f.y),
                      mix(mix(hash(n+113.0), hash(n+114.0), f.x),
                          mix(hash(n+170.0), hash(n+171.0), f.x), f.y), f.z);
      return res;
    }
    void main(){
      vNormal = normal;
      vPos = position;
      float n = noise(position* (1.0 + audioMid*3.0) + time*0.6);
      float bass = audioBass;
      float disp = (n*0.45 + bass*0.6) * (0.35 + audioMid*0.6);
      vec3 newPos = position + normal * disp;
      vNoise = n;
      gl_Position = projectionMatrix * modelViewMatrix * vec4(newPos, 1.0);
    }`,
  fragmentShader: `precision highp float;
    uniform float time;
    uniform float audioHigh;
    uniform vec3 colorA;
    uniform vec3 colorB;
    varying vec3 vNormal;
    varying vec3 vPos;
    varying float vNoise;
    void main(){
      float fres = pow(1.0 - dot(normalize(vNormal), vec3(0.0,0.0,1.0)), 2.0);
      vec3 base = mix(colorA, colorB, smoothstep(-0.2, 0.8, vPos.y + vNoise*0.6));
      // rim + audio shimmer
      float rim = smoothstep(0.4, 1.0, 1.0 - abs(vPos.y) + vNoise*0.5);
      vec3 glow = vec3(0.8,0.35,0.9) * pow(audioHigh + 0.2, 1.8) * rim;
      vec3 col = base + glow * 0.9;
      // subtle stripes
      float stripes = abs(sin((vPos.x+vPos.y+vPos.z + time*0.5)*6.0 + vNoise*4.0));
      col += 0.06 * vec3(1.0,0.9,1.0) * (1.0 - stripes);
      gl_FragColor = vec4(col, 1.0);
    }`,
  wireframe: false,
  transparent: false,
  side: THREE.DoubleSide
});
const mainMesh = new THREE.Mesh(geom, mainMat);
mainMesh.position.y = 0.05;
scene.add(mainMesh);

// rim light point
const rimLight = new THREE.PointLight(0xff66b0, 0.6, 20);
rimLight.position.set(-6, 4, 8);
scene.add(rimLight);
const rimLight2 = new THREE.PointLight(0x00d9ff, 0.9, 30);
rimLight2.position.set(5, -3, -6);
scene.add(rimLight2);

// particle field (points with shader)
let particleSystem = null;
let particleUniforms = null;

function buildParticles(count = parseInt(countSlider.value)) {
  if (particleSystem) {
    scene.remove(particleSystem);
    particleSystem.geometry.dispose();
    particleSystem.material.dispose();
    particleSystem = null;
  }
  const pGeo = new THREE.BufferGeometry();
  const positions = new Float32Array(count * 3);
  const sizes = new Float32Array(count);
  const seeds = new Float32Array(count);
  for (let i = 0; i < count; i++) {
    const r = THREE.MathUtils.randFloatSpread(10); // random spread in cube radius
    positions[i * 3 + 0] = THREE.MathUtils.randFloatSpread(12);
    positions[i * 3 + 1] = THREE.MathUtils.randFloatSpread(6);
    positions[i * 3 + 2] = THREE.MathUtils.randFloatSpread(12);
    sizes[i] = Math.random() * 3.0 + 0.6;
    seeds[i] = Math.random() * 10;
  }
  pGeo.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  pGeo.setAttribute('aSize', new THREE.BufferAttribute(sizes, 1));
  pGeo.setAttribute('aSeed', new THREE.BufferAttribute(seeds, 1));

  particleUniforms = {
    time: { value: 0 },
    audio: { value: 0 },
    resolution: { value: new THREE.Vector2(innerWidth, innerHeight) }
  };

  const pMat = new THREE.ShaderMaterial({
    uniforms: particleUniforms,
    vertexShader: `precision highp float;
      attribute float aSize;
      attribute float aSeed;
      uniform float time;
      uniform float audio;
      varying float vAlpha;
      void main(){
        vec3 pos = position;
        // wobble + orbit influenced by audio
        float t = time * 0.4 + aSeed * 1.7;
        pos.x += 0.6 * sin(t*0.8 + position.y*0.6 + audio*6.0);
        pos.z += 0.6 * cos(t*0.6 + position.x*0.5 + audio*4.0);
        pos.y += 0.5 * sin(t*0.3 + audio*3.0);
        vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
        gl_PointSize = (aSize * (60.0 / -mvPosition.z)) * (1.0 + audio*1.6);
        gl_Position = projectionMatrix * mvPosition;
        vAlpha = 0.2 + 0.8 * smoothstep(-2.0, 3.0, -mvPosition.z);
      }`,
    fragmentShader: `precision highp float;
      varying float vAlpha;
      uniform float audio;
      void main(){
        vec2 uv = gl_PointCoord - 0.5;
        float r = length(uv);
        float circle = smoothstep(0.5, 0.1, r);
        // halo
        float halo = exp(-r*6.0) * 0.9;
        vec3 col = vec3(0.1, 0.6, 0.9) * (0.8 + audio*0.8) + vec3(1.0,0.45,0.8) * (audio*0.6);
        gl_FragColor = vec4(col, circle * vAlpha * (0.5 + audio*0.5) + halo*0.2);
      }`,
    transparent: true,
    depthWrite: false,
    blending: THREE.AdditiveBlending
  });

  particleSystem = new THREE.Points(pGeo, pMat);
  scene.add(particleSystem);
}
buildParticles();

// respond to particle count changes
countSlider.addEventListener('input', () => {
  buildParticles(parseInt(countSlider.value));
});

// === RENDER LOOP ===
let last = performance.now();
function splitBands(freqs) {
  // simple division: bass < 160, mid 160-2500, high >2500
  const sr = audioCtx?.sampleRate || 44100;
  const nyq = sr / 2;
  const len = freqs.length;
  const hzPerBin = nyq / len;
  let bass = 0, mids = 0, highs = 0;
  let cb=0, cm=0, ch=0;
  for (let i = 0; i < len; i++) {
    const hz = i * hzPerBin;
    const v = freqs[i] / 255;
    if (hz < 160) { bass += v; cb++; }
    else if (hz < 2500) { mids += v; cm++; }
    else { highs += v; ch++; }
  }
  return {
    bass: cb ? (bass / cb) : 0,
    mids: cm ? (mids / cm) : 0,
    highs: ch ? (highs / ch) : 0
  };
}

function animate(now) {
  now *= 0.001;
  const dt = (now - last);
  last = now;

  // poll audio
  let audioLevel = 0, bands = {bass:0,mids:0,highs:0};
  if (analyser && dataArray) {
    analyser.getByteFrequencyData(dataArray);
    // estimate energy (rms-like)
    let sum = 0;
    for (let i=0;i<dataArray.length;i++){ sum += dataArray[i]*dataArray[i]; }
    audioLevel = Math.sqrt(sum / dataArray.length) / 255;
    bands = splitBands(dataArray);
  } else {
    // idle subtle movement
    audioLevel = 0.08 + 0.06 * Math.sin(now * 0.8);
    bands = { bass: 0.06 + 0.04*Math.sin(now*0.6), mids:0.05, highs:0.03};
  }

  const sensitivity = parseFloat(sens.value);

  // update uniforms
  bgMat.uniforms.time.value = now * 1.2;
  bgMat.uniforms.audio.value = Math.min(1.0, audioLevel * sensitivity * 1.2);

  mainMat.uniforms.time.value = now;
  mainMat.uniforms.audioBass.value = Math.min(1.0, bands.bass * sensitivity * 1.6);
  mainMat.uniforms.audioMid.value = Math.min(1.0, bands.mids * sensitivity * 1.2);
  mainMat.uniforms.audioHigh.value = Math.min(1.0, bands.highs * sensitivity * 1.8);

  if (particleUniforms) {
    particleUniforms.time.value = now * 1.6;
    particleUniforms.audio.value = Math.min(1.0, audioLevel * sensitivity * 1.4);
  }

  // subtle mesh rotation mapped to audio
  mainMesh.rotation.y += 0.2 * dt * (1.0 + mainMat.uniforms.audioMid.value * 3.0);
  mainMesh.rotation.x = 0.12 * Math.sin(now*0.35);

  controls.update();
  renderer.render(scene, camera);
  requestAnimationFrame(animate);
}
requestAnimationFrame(animate);

// handle resize
window.addEventListener('resize', () => {
  renderer.setSize(innerWidth, innerHeight, false);
  camera.aspect = innerWidth / innerHeight;
  camera.updateProjectionMatrix();
  bgMat.uniforms.resolution.value.set(innerWidth, innerHeight);
  if (particleUniforms) particleUniforms.resolution.value.set(innerWidth, innerHeight);
});

// unlock audio context on user gesture
window.addEventListener('pointerdown', async () => {
  if (audioCtx && audioCtx.state === 'suspended') {
    try { await audioCtx.resume(); } catch {}
  }
}, { once: false });

/* Small UX: show if WebGL not available */
if (!renderer.capabilities.isWebGL2 && !renderer.capabilities.isWebGL1) {
  const msg = document.createElement('div');
  msg.style.position = 'fixed';
  msg.style.left = '50%';
  msg.style.top = '50%';
  msg.style.transform = 'translate(-50%,-50%)';
  msg.style.padding = '18px 22px';
  msg.style.background = 'rgba(0,0,0,0.6)';
  msg.style.borderRadius = '10px';
  msg.style.color = '#fff';
  msg.textContent = 'WebGL not available in this browser.';
  document.body.appendChild(msg);
}

</script>
</body>
</html>
