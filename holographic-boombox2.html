<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Holographic Boombox Visualizer</title>
<style>
  body {
    background: black;
    margin: 0;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .boombox {
    width: 600px;
    height: 300px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background: rgba(0,0,0,0.3);
    border: 2px solid rgba(0,255,255,0.5);
    box-shadow: 0 0 25px cyan;
    padding: 20px;
    border-radius: 20px;
    position: relative;
  }
  .speaker {
    width: 200px;
    height: 200px;
    background: rgba(0,0,0,0.1);
    border: 2px solid rgba(0,255,255,0.3);
    box-shadow: inset 0 0 20px rgba(0,255,255,0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }
  .speaker canvas {
    width: 100%;
    height: 100%;
  }
  .controls {
    position: absolute;
    bottom: -60px;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 15px;
  }
  .controls input, .controls button {
    background: black;
    color: cyan;
    border: 1px solid cyan;
    border-radius: 5px;
    padding: 5px 10px;
    cursor: pointer;
  }
  .particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: cyan;
    border-radius: 50%;
    pointer-events: none;
  }
</style>
</head>
<body>
  <div class="boombox">
    <div class="speaker"><canvas id="leftSpeaker"></canvas></div>
    <div class="speaker"><canvas id="rightSpeaker"></canvas></div>
    <div class="controls">
      <input type="file" id="fileInput" accept="audio/*">
      <button id="micBtn">🎤 Mic</button>
    </div>
  </div>

<script>
const leftCanvas = document.getElementById("leftSpeaker");
const rightCanvas = document.getElementById("rightSpeaker");
const lctx = leftCanvas.getContext("2d");
const rctx = rightCanvas.getContext("2d");

let audioCtx, analyser, dataArray, sourceNode, micStream;

function resizeCanvas() {
  [leftCanvas, rightCanvas].forEach(c => {
    c.width = c.clientWidth;
    c.height = c.clientHeight;
  });
}
window.addEventListener("resize", resizeCanvas);
resizeCanvas();

function makeAnalyser() {
  audioCtx = new (window.AudioContext||window.webkitAudioContext)();
  analyser = audioCtx.createAnalyser();
  analyser.fftSize = 512;
  dataArray = new Uint8Array(analyser.frequencyBinCount);
}
function connect(node) {
  node.connect(analyser);
  analyser.connect(audioCtx.destination);
}
function stopAll() {
  if(audioCtx) audioCtx.close();
  if(micStream) micStream.getTracks().forEach(t=>t.stop());
}

document.getElementById("fileInput").onchange = e=>{
  if(!e.target.files[0]) return;
  stopAll();
  makeAnalyser();
  const audio = new Audio(URL.createObjectURL(e.target.files[0]));
  audio.loop = true;
  audio.crossOrigin="anonymous";
  sourceNode = audioCtx.createMediaElementSource(audio);
  connect(sourceNode);
  audio.play();
  draw();
};

document.getElementById("micBtn").onclick = async()=>{
  stopAll();
  micStream = await navigator.mediaDevices.getUserMedia({audio:true});
  makeAnalyser();
  sourceNode = audioCtx.createMediaStreamSource(micStream);
  connect(sourceNode);
  draw();
};

// Particle system
let particles=[];
function spawnParticle(x,y){
  const p=document.createElement("div");
  p.className="particle";
  document.body.appendChild(p);
  const angle=Math.random()*Math.PI*2;
  const speed=1+Math.random()*2;
  particles.push({el:p,x,y,vx:Math.cos(angle)*speed,vy:Math.sin(angle)*speed,life:100});
}
function updateParticles(){
  particles.forEach((p,i)=>{
    p.x+=p.vx;
    p.y+=p.vy;
    p.life--;
    p.el.style.left=p.x+"px";
    p.el.style.top=p.y+"px";
    p.el.style.opacity=p.life/100;
    if(p.life<=0){
      p.el.remove();
      particles.splice(i,1);
    }
  });
}

// Draw spline circle
function drawSpline(ctx,cx,cy,radius,amp,color){
  ctx.clearRect(0,0,ctx.canvas.width,ctx.canvas.height);
  ctx.beginPath();
  const points=64;
  const step=Math.PI*2/points;
  for(let i=0;i<=points;i++){
    const angle=i*step;
    const variation=Math.sin(angle*4+Date.now()*0.003)*amp;
    const r=radius+variation;
    const x=cx+Math.cos(angle)*r;
    const y=cy+Math.sin(angle)*r;
    if(i===0) ctx.moveTo(x,y);
    else {
      const prevAngle=(i-0.5)*step;
      const midX=cx+Math.cos(prevAngle)*(radius+variation);
      const midY=cy+Math.sin(prevAngle)*(radius+variation);
      ctx.quadraticCurveTo(midX,midY,x,y);
    }
  }
  ctx.strokeStyle=color;
  ctx.lineWidth=1;
  ctx.shadowBlur=20;
  ctx.shadowColor=color;
  ctx.stroke();
}

// Animation
function draw(){
  requestAnimationFrame(draw);
  analyser.getByteFrequencyData(dataArray);
  const bass = dataArray.slice(0, dataArray.length/4).reduce((a,b)=>a+b)/ (dataArray.length/4);
  const treble = dataArray.slice(dataArray.length/2).reduce((a,b)=>a+b)/(dataArray.length/2);
  const lcx=leftCanvas.width/2, lcy=leftCanvas.height/2;
  const rcx=rightCanvas.width/2, rcy=rightCanvas.height/2;
  const ampL = bass/50;
  const ampR = treble/50;
  drawSpline(lctx,lcx,lcy,70,ampL,`hsl(${200+bass/3},100%,60%)`);
  drawSpline(rctx,rcx,rcy,70,ampR,`hsl(${300+treble/3},100%,60%)`);
  if(bass>180){ // spawn particles on bass hits
    const bb=document.querySelector(".boombox").getBoundingClientRect();
    spawnParticle(bb.left+lcx,bb.top+lcy);
    spawnParticle(bb.left+rcx,bb.top+rcy);
  }
  updateParticles();
}
</script>
</body>
</html>
