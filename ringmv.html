<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Thin Symmetrical Music Ring with Particles</title>
<style>
  html,body{margin:0;height:100%;background:#000;overflow:hidden}
  canvas{width:100%;height:100%;display:block;background:#000}
  #file,#micBtn{position:fixed;top:10px;z-index:10;color:white}
  #micBtn{left:150px}
</style>
</head>
<body>
<canvas id="c"></canvas>
<input id="file" type="file" accept="audio/*">
<button id="micBtn">Use Mic</button>
<script>
const canvas=document.getElementById("c"),ctx=canvas.getContext("2d");
let audioCtx,analyser,src,dataArray,waveArray,sourceNode,micStream;
let particles=[]; let hue=200;

function resize(){canvas.width=innerWidth;canvas.height=innerHeight}
addEventListener("resize",resize);resize();

// audio setup
function makeAnalyser(){
  audioCtx=new (window.AudioContext||window.webkitAudioContext)();
  analyser=audioCtx.createAnalyser();
  analyser.fftSize=1024;
  dataArray=new Uint8Array(analyser.frequencyBinCount);
  waveArray=new Uint8Array(analyser.fftSize);
}
function connect(node){node.connect(analyser); analyser.connect(audioCtx.destination)}

// file input
file.onchange=e=>{
  if(!file.files[0])return;
  stopAll();
  makeAnalyser();
  const audio=new Audio(URL.createObjectURL(file.files[0]));
  audio.loop=true; audio.crossOrigin="anonymous";
  sourceNode=audioCtx.createMediaElementSource(audio);
  connect(sourceNode); audio.play();
  draw();
};

// mic input
micBtn.onclick=async()=>{
  stopAll();
  micStream=await navigator.mediaDevices.getUserMedia({audio:true});
  makeAnalyser();
  sourceNode=audioCtx.createMediaStreamSource(micStream);
  connect(sourceNode); draw();
};

function stopAll(){
  if(audioCtx) audioCtx.close();
  if(micStream) micStream.getTracks().forEach(t=>t.stop());
  particles=[];
}

// particle class
class Particle{
  constructor(x,y,angle,speed,color){
    this.x=x; this.y=y;
    this.vx=Math.cos(angle)*speed;
    this.vy=Math.sin(angle)*speed;
    this.life=1; this.color=color;
  }
  update(){
    this.x+=this.vx; this.y+=this.vy;
    this.vx*=0.98; this.vy*=0.98;
    this.life-=0.01;
  }
  draw(ctx){
    ctx.globalAlpha=this.life;
    ctx.fillStyle=this.color;
    ctx.beginPath();
    ctx.arc(this.x,this.y,2,0,Math.PI*2);
    ctx.fill();
    ctx.globalAlpha=1;
  }
}

// draw loop
function draw(){
  requestAnimationFrame(draw);
  if(!analyser)return;
  analyser.getByteFrequencyData(dataArray);
  analyser.getByteTimeDomainData(waveArray);

  ctx.fillStyle="rgba(0,0,0,0.25)";
  ctx.fillRect(0,0,canvas.width,canvas.height);

  const w=canvas.width,h=canvas.height,cx=w/2,cy=h/2;
  const base=Math.min(w,h)/4;
  hue=(hue+0.5)%360;

  // average loudness for particles
  let avg=0; for(let v of dataArray)avg+=v; avg/=dataArray.length;

  if(avg>120){
    for(let i=0;i<6;i++){
      const angle=Math.random()*Math.PI*2;
      const speed=2+Math.random()*3;
      const color=`hsl(${hue},100%,60%)`;
      particles.push(new Particle(cx,cy,angle,speed,color));
    }
  }

  // symmetrical waveform ring (1px thin)
  ctx.save();
  ctx.translate(cx,cy);
  ctx.beginPath();
  const points=dataArray.length;
  for(let i=0;i<points;i++){
    const angle=(i/points)*Math.PI*2;
    // take mirrored value (average of bin and its opposite)
    const j=(i+points/2)%points|0;
    const val=(dataArray[i]+dataArray[j])/510; // 0..1
    const r=base+val*100;
    const x=Math.cos(angle)*r;
    const y=Math.sin(angle)*r;
    if(i===0) ctx.moveTo(x,y); else ctx.lineTo(x,y);
  }
  ctx.closePath();
  ctx.strokeStyle=`hsl(${hue},100%,70%)`;
  ctx.lineWidth=1;
  ctx.shadowBlur=0;
  ctx.stroke();
  ctx.restore();

  // update/draw particles
  for(let i=particles.length-1;i>=0;i--){
    const p=particles[i]; p.update(); p.draw(ctx);
    if(p.life<=0) particles.splice(i,1);
  }
}
</script>
</body>
</html>
