<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Multiverse — Parallel Planes of Dimensions</title>
<style>
  :root{
    --bg:#05070b; --fg:#c9f6ff; --accent:#00e5ff; --accent2:#58ffa7; --panel:#0b1118; --grid:#0f3140;
  }
  html,body{height:100%;}
  body{margin:0;background:radial-gradient(1200px 1200px at 70% 25%, #0d1721 0%, #08131b 40%, var(--bg) 100%);color:var(--fg);font:13px/1.45 ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;overflow:hidden}
  .ui{position:fixed;inset:12px auto auto 12px;display:flex;gap:.5rem;flex-wrap:wrap;z-index:10}
  .panel{background:rgba(8,16,22,.65);backdrop-filter:blur(6px);border:1px solid #12353f;border-radius:12px;padding:10px 12px;box-shadow:0 0 14px rgba(0,229,255,.08)}
  .panel h2{margin:0 0 8px 0;font-size:12px;letter-spacing:.1em;text-transform:uppercase;color:#a8efff}
  .row{display:flex;align-items:center;gap:.5rem;margin:.25rem 0}
  .row label{color:#8bc7d2;font-size:12px;width:92px}
  .row input,.row select{accent-color:var(--accent)}
  .btn{cursor:pointer;border:1px solid #0d2a34;background:#0a1820;color:var(--fg);padding:6px 10px;border-radius:10px}
  .btn:hover{background:#0d2029}

  /* tiny legend */
  .legend{position:fixed;inset:auto 12px 12px auto;pointer-events:none;background:rgba(6,12,16,.45);border:1px solid #0c2a33;border-radius:10px;padding:8px 10px;color:#8fd7e3}
</style>
</head>
<body>
  <canvas id="c"></canvas>

  <div class="ui">
    <div class="panel">
      <h2>Multiverse Controls</h2>
      <div class="row"><label for="planes">Planes</label><input id="planes" type="range" min="3" max="24" step="1" value="12"></div>
      <div class="row"><label for="speed">Scroll Speed</label><input id="speed" type="range" min="0" max="1.5" step="0.01" value="0.4"></div>
      <div class="row"><label for="warp">Warp Amount</label><input id="warp" type="range" min="0" max="1" step="0.01" value="0.35"></div>
      <div class="row"><label for="portalDensity">Portal Density</label><input id="portalDensity" type="range" min="0" max="1" step="0.01" value="0.35"></div>
      <div class="row"><label for="theme">Theme</label>
        <select id="theme">
          <option value="aqua">Aqua (default)</option>
          <option value="violet">Violet Nebula</option>
          <option value="emerald">Emerald Grid</option>
          <option value="amber">Amber Flux</option>
        </select>
      </div>
      <div class="row"><button class="btn" id="shuffle">🎲 Shuffle Universe</button><button class="btn" id="pause">⏸️ Pause</button></div>
    </div>
  </div>

  <div class="legend">Drag to orbit • Wheel/Pinch to zoom • Double‑click to focus</div>

<script>
(() => {
  // ----- Setup -----
  const c = document.getElementById('c');
  const x = c.getContext('2d');
  const dpr = Math.max(1, Math.min(2, window.devicePixelRatio||1));
  function size(){ c.width = Math.floor(innerWidth*dpr); c.height=Math.floor(innerHeight*dpr); x.setTransform(dpr,0,0,dpr,0,0);} size();
  addEventListener('resize', size);

  // UI
  const ui = {
    planes: document.getElementById('planes'),
    speed: document.getElementById('speed'),
    warp: document.getElementById('warp'),
    portalDensity: document.getElementById('portalDensity'),
    theme: document.getElementById('theme'),
    shuffle: document.getElementById('shuffle'),
    pause: document.getElementById('pause')
  };
  let paused = false;
  ui.pause.addEventListener('click', ()=>{ paused=!paused; ui.pause.textContent = paused? '▶️ Play':'⏸️ Pause'; });

  // Camera (simple orbit controls)
  let cam = { fov: 600, z: 900, ry: 0.3, rx: -0.12, tx:0, ty:0 };
  let isDown=false, lx=0, ly=0; let target = {ry:cam.ry, rx:cam.rx, z:cam.z};
  c.addEventListener('pointerdown', e=>{ isDown=true; lx=e.clientX; ly=e.clientY; });
  addEventListener('pointerup', ()=>{ isDown=false; });
  addEventListener('pointermove', e=>{ if(!isDown) return; const dx=(e.clientX-lx)/innerWidth, dy=(e.clientY-ly)/innerHeight; target.ry += dx*2.4; target.rx += dy*2.0; lx=e.clientX; ly=e.clientY; });
  c.addEventListener('wheel', e=>{ target.z = Math.max(200, Math.min(2600, target.z + e.deltaY)); });
  c.addEventListener('dblclick', ()=>{ target.ry = 0; target.rx = -0.12; target.z = 900; });

  // Themes
  const THEMES = {
    aqua:   { grid:'#0f3140', line:'#aefcff', ring:'rgba(0,229,255,0.7)', portal:'#6af7ff', star:'#8dd6ff' },
    violet: { grid:'#2a1346', line:'#e2b3ff', ring:'rgba(168,85,247,0.75)', portal:'#edc7ff', star:'#f1e1ff' },
    emerald:{ grid:'#123a2a', line:'#a8ffd6', ring:'rgba(16,185,129,0.75)', portal:'#c4ffe8', star:'#baffea' },
    amber:  { grid:'#3a2a12', line:'#ffe0a3', ring:'rgba(245,158,11,0.75)', portal:'#fff1c7', star:'#ffe9b8' },
  };
  let theme = THEMES.aqua;
  ui.theme.addEventListener('change', ()=>{ theme = THEMES[ui.theme.value] || THEMES.aqua; });

  // Universe config
  let rngSeed = (Math.random()*1e9)|0;
  function rand(){ // xorshift32
    rngSeed ^= rngSeed<<13; rngSeed ^= rngSeed>>>17; rngSeed ^= rngSeed<<5; return (rngSeed>>>0)/4294967295;
  }
  ui.shuffle.addEventListener('click', ()=>{ rngSeed = (Math.random()*1e9)|0; buildUniverse(); });

  const planes = [];
  const portals = [];
  const stars = [];

  function buildUniverse(){
    planes.length = 0; portals.length = 0; stars.length = 0;
    const count = parseInt(ui.planes.value,10);
    const spacing = 220; // z-distance between planes
    for(let i=0;i<count;i++){
      planes.push({ z:  i*spacing, rot: (rand()*0.6-0.3), wiggle: rand()*TAU });
      // optional portal on plane
      if (rand() < parseFloat(ui.portalDensity.value)){
        portals.push({ plane:i, x:(rand()*800-400), y:(rand()*500-250), r: 40 + rand()*90, ph: rand()*TAU });
      }
    }
    // starfield (parallax background)
    const starCount = 600;
    for(let i=0;i<starCount;i++){
      stars.push({ x: (rand()-0.5)*4000, y:(rand()-0.5)*2400, z: 1000 + rand()*8000, s: rand()*2+0.5, tw: rand()*TAU });
    }
  }

  // Math helpers
  const TAU = Math.PI*2; const sin=Math.sin, cos=Math.cos; const abs=Math.abs; const min=Math.min; const max=Math.max;
  function project(px,py,pz){
    // rotate by camera rx,ry; translate by cam position; perspective divide
    const sy = sin(cam.ry), cy = cos(cam.ry);
    const sx = sin(cam.rx), cx = cos(cam.rx);
    // camera transform
    let xw = px - cam.tx, yw = py - cam.ty, zw = pz + 100; // push scene forward a bit
    // rotate around Y (yaw)
    let xr =  cy*xw + 0* yw + sy*zw;
    let yr =  sx*(sy*xw + 0*yw - cy*zw) + cx*yw; // pitch then add y
    let zr = -cx*(sy*xw + 0*yw - cy*zw) + sx*yw;
    // move camera in Z
    zr += cam.z;
    const f = cam.fov / (zr || 1);
    return { x: innerWidth/2 + xr*f, y: innerHeight/2 + yr*f, d: zr };
  }

  function drawGridPlane(z, rot, wiggle, t){
    // A plane centered at origin (x,y in [-W..W],[-H..H]) placed at depth z
    const W = 900, H = 700; // plane half sizes in world units
    const step = 90; // grid cell size
    const warp = parseFloat(ui.warp.value);

    x.globalAlpha = 0.9;
    x.lineWidth = 1.2;
    x.strokeStyle = theme.grid;

    // subtle plane wobble (dimensional shear)
    const shear = Math.sin(t*0.7 + wiggle)*warp*0.8;

    // vertical lines (constant x)
    for(let X=-W; X<=W; X+=step){
      x.beginPath();
      for(let Y=-H; Y<=H; Y+= step){
        const yy = Y + Math.sin((X+ t*60)*0.01 + t*0.6)*warp*20;
        let px =  X;
        let py =  yy + shear*X*0.002;
        let pz =  z + Math.sin((X+Y)*0.002 + t*0.5)*warp*40;
        // rotate plane around its own Y axis
        const s = Math.sin(rot), c = Math.cos(rot);
        const rx =  c*px + s*0 + 0; // y unused here for rotation around Y in plane space
        const rz = -s*px + c*0;
        const P = project(rx, py, pz+rz);
        if(Y===-H) x.moveTo(P.x,P.y); else x.lineTo(P.x,P.y);
      }
      x.stroke();
    }

    // horizontal lines (constant y)
    for(let Y=-H; Y<=H; Y+=step){
      x.beginPath();
      for(let X=-W; X<=W; X+= step){
        const xx = X + Math.sin((Y+ t*60)*0.01 + t*0.6)*warp*20;
        let px =  xx + shear*Y*0.002;
        let py =  Y;
        let pz =  z + Math.sin((X+Y)*0.002 + t*0.5)*warp*40;
        const s = Math.sin(rot), c = Math.cos(rot);
        const rx =  c*px + s*0;
        const rz = -s*px + c*0;
        const P = project(rx, py, pz+rz);
        if(X===-W) x.moveTo(P.x,P.y); else x.lineTo(P.x,P.y);
      }
      x.stroke();
    }

    // glowing horizon
    const h0 = project(0,0,z).y;
    const grad = x.createLinearGradient(0,h0-80,0,h0+80);
    grad.addColorStop(0,'rgba(0,0,0,0)');
    grad.addColorStop(0.5, theme.line);
    grad.addColorStop(1,'rgba(0,0,0,0)');
    x.fillStyle = grad; x.fillRect(0, h0-80, innerWidth, 160);
  }

  function drawPortal(portal, planeZ, t){
    // portal sits in plane space at (x,y) with radius r
    const s = Math.sin(t*0.9 + portal.ph)*0.6 + 0.4;
    const rr = portal.r * (1.0 + s*0.25);
    const P = project(portal.x, portal.y, planeZ);
    const ring = x.createRadialGradient(P.x,P.y, rr*0.2, P.x,P.y, rr*1.2);
    ring.addColorStop(0, theme.portal);
    ring.addColorStop(0.6, theme.ring);
    ring.addColorStop(1, 'rgba(0,0,0,0)');
    x.globalCompositeOperation = 'lighter';
    x.fillStyle = ring; x.beginPath(); x.arc(P.x,P.y, rr*1.2, 0, TAU); x.fill();
    x.globalCompositeOperation = 'source-over';

    // connection beams (find nearest other portal)
    let best=null, bestDist=1e9;
    for(const q of portals){ if(q===portal) continue; if(q._pz==null) continue; const Q=project(q.x,q.y,q._pz); const dx=Q.x-P.x, dy=Q.y-P.y; const d=dx*dx+dy*dy; if(d<bestDist){bestDist=d; best=Q;} }
    if(best){
      x.strokeStyle = theme.ring; x.lineWidth = 1.6; x.globalAlpha = 0.8;
      x.beginPath(); x.moveTo(P.x,P.y); x.lineTo(best.x,best.y); x.stroke(); x.globalAlpha = 1;
    }
  }

  function drawStars(t){
    x.fillStyle = 'rgba(0,0,0,0.65)'; x.fillRect(0,0,innerWidth,innerHeight);
    for(const s of stars){
      const sc = 1 + (Math.sin(t*0.6 + s.tw)*0.25+0.25);
      const P = project(s.x, s.y, s.z);
      if(P.d>0){
        x.fillStyle = theme.star; x.globalAlpha = 0.6 + 0.4*Math.sin(t + s.tw);
        x.fillRect(P.x, P.y, sc, sc);
      }
    }
    x.globalAlpha = 1;
  }

  // Build and animate
  buildUniverse();

  let t0 = performance.now();
  function tick(now){
    requestAnimationFrame(tick);
    if (paused) return;
    const dt = (now - t0) / 1000; t0 = now; const t = now/1000;

    // ease camera to target
    cam.ry += (target.ry - cam.ry)*0.12; cam.rx += (target.rx - cam.rx)*0.12; cam.z += (target.z - cam.z)*0.1;

    // background
    drawStars(t);

    // scroll planes forward, wrap around
    const spd = parseFloat(ui.speed.value) * 120; // units per second
    const spacing = 220;
    for(let i=0;i<planes.length;i++){
      const p = planes[i];
      p.z -= spd*dt; if(p.z < -spacing){ p.z += spacing*planes.length; } // wrap
    }

    // draw planes from farthest to nearest
    const order = [...planes].sort((a,b)=>b.z - a.z);
    for(const p of order){ drawGridPlane(p.z, p.rot, p.wiggle, t); }

    // position portals on their current plane depths and draw
    for(const prt of portals){
      const plane = planes[prt.plane];
      prt._pz = plane.z; // cache projected z for linking
      drawPortal(prt, prt._pz, t);
    }

    // subtle foreground bloom
    const g = x.createLinearGradient(0,0,0,innerHeight);
    g.addColorStop(0,'rgba(0,0,0,0.0)');
    g.addColorStop(1,'rgba(0,0,0,0.2)');
    x.fillStyle = g; x.fillRect(0,0,innerWidth,innerHeight);

    // crosshair center
    const P = project(0,0,0);
    x.strokeStyle = theme.line; x.lineWidth = 1; x.globalAlpha=0.5; x.beginPath();
    x.moveTo(P.x-8,P.y); x.lineTo(P.x+8,P.y); x.moveTo(P.x,P.y-8); x.lineTo(P.x,P.y+8); x.stroke(); x.globalAlpha=1;
  }
  requestAnimationFrame(tick);

  // React to control changes
  function recreate(){ buildUniverse(); }
  ui.planes.addEventListener('input', recreate);
  ui.portalDensity.addEventListener('input', recreate);

})();
</script>
</body>
</html>
