<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Holographic 3D Boom Box</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            overflow: hidden;
            font-family: 'Courier New', monospace;
        }

        #container {
            width: 100vw;
            height: 100vh;
            perspective: 1000px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .boombox {
            width: 600px;
            height: 300px;
            position: relative;
            transform-style: preserve-3d;
            animation: rotate 20s infinite linear;
        }

        .face {
            position: absolute;
            border: 2px solid #00ffff;
            background: rgba(0, 255, 255, 0.1);
            box-shadow: 
                0 0 20px #00ffff,
                inset 0 0 20px rgba(0, 255, 255, 0.2);
        }

        .front, .back {
            width: 600px;
            height: 300px;
        }

        .front {
            transform: translateZ(150px);
        }

        .back {
            transform: translateZ(-150px) rotateY(180deg);
        }

        .left, .right {
            width: 300px;
            height: 300px;
        }

        .left {
            transform: rotateY(-90deg) translateZ(150px);
        }

        .right {
            transform: rotateY(90deg) translateZ(150px);
        }

        .top, .bottom {
            width: 600px;
            height: 300px;
        }

        .top {
            transform: rotateX(90deg) translateZ(150px);
        }

        .bottom {
            transform: rotateX(-90deg) translateZ(150px);
        }

        /* Wireframe vertices */
        .vertex {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #00ffff;
            border-radius: 50%;
            box-shadow: 0 0 15px #00ffff;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }

        @keyframes rotate {
            0% { transform: rotateY(-10deg) rotateX(10deg); }
            50% { transform: rotateY(10deg) rotateX(10deg); }
            100% { transform: rotateY(-10deg) rotateX(10deg); }
        }

        /* Holographic displays */
        .display {
            position: absolute;
            background: rgba(0, 255, 255, 0.15);
            border: 1px solid #00ffff;
            border-radius: 10px;
            box-shadow: 0 0 30px #00ffff;
            overflow: hidden;
        }

        .main-display {
            width: 200px;
            height: 100px;
            top: 50px;
            left: 200px;
            animation: hologram-flicker 3s infinite;
        }

        .speaker-left, .speaker-right {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            top: 90px;
        }

        .speaker-left {
            left: 30px;
        }

        .speaker-right {
            right: 30px;
        }

        .control-panel {
            width: 150px;
            height: 60px;
            bottom: 30px;
            left: 225px;
        }

        /* Additional detailed components */
        .antenna {
            position: absolute;
            width: 3px;
            height: 80px;
            background: linear-gradient(to top, #00ffff, #ff0080);
            top: -80px;
            left: 50px;
            box-shadow: 0 0 15px #00ffff;
            animation: antenna-glow 2s infinite alternate;
        }

        .antenna::after {
            content: '';
            position: absolute;
            top: -10px;
            left: -3px;
            width: 9px;
            height: 9px;
            background: #ff0080;
            border-radius: 50%;
            box-shadow: 0 0 20px #ff0080;
        }

        @keyframes antenna-glow {
            0% { box-shadow: 0 0 15px #00ffff; }
            100% { box-shadow: 0 0 25px #00ffff, 0 0 35px #ff0080; }
        }

        .handle {
            position: absolute;
            width: 20px;
            height: 100px;
            border: 2px solid #00ffff;
            border-radius: 10px;
            background: rgba(0, 255, 255, 0.1);
            top: -120px;
            left: 290px;
            box-shadow: 0 0 20px #00ffff;
        }

        .knob {
            position: absolute;
            width: 25px;
            height: 25px;
            border: 2px solid #00ffff;
            border-radius: 50%;
            background: rgba(0, 255, 255, 0.2);
            box-shadow: 0 0 15px #00ffff;
        }

        .volume-knob {
            bottom: 80px;
            left: 50px;
            animation: knob-spin 8s infinite linear;
        }

        .tuning-knob {
            bottom: 80px;
            right: 50px;
            animation: knob-spin 12s infinite linear reverse;
        }

        @keyframes knob-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .led-indicator {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: led-blink 1.5s infinite;
        }

        .power-led {
            top: 20px;
            left: 20px;
            background: #00ff00;
            box-shadow: 0 0 15px #00ff00;
        }

        .record-led {
            top: 20px;
            left: 40px;
            background: #ff0000;
            box-shadow: 0 0 15px #ff0000;
            animation-delay: 0.5s;
        }

        .bluetooth-led {
            top: 20px;
            left: 60px;
            background: #0080ff;
            box-shadow: 0 0 15px #0080ff;
            animation-delay: 1s;
        }

        .wifi-led {
            top: 20px;
            left: 80px;
            background: #ff8000;
            box-shadow: 0 0 15px #ff8000;
            animation-delay: 0.3s;
        }

        .aux-led {
            top: 20px;
            left: 100px;
            background: #8000ff;
            box-shadow: 0 0 15px #8000ff;
            animation-delay: 0.8s;
        }

        .battery-led {
            top: 20px;
            right: 20px;
            background: #ffff00;
            box-shadow: 0 0 15px #ffff00;
            animation-delay: 1.2s;
        }

        .signal-led {
            top: 20px;
            right: 40px;
            background: #ff0080;
            box-shadow: 0 0 15px #ff0080;
            animation-delay: 0.7s;
        }

        .stereo-led {
            top: 40px;
            left: 20px;
            background: #00ffff;
            box-shadow: 0 0 15px #00ffff;
            animation-delay: 1.5s;
        }

        .bass-led {
            bottom: 20px;
            left: 20px;
            background: #ff4000;
            box-shadow: 0 0 15px #ff4000;
            animation-delay: 0.2s;
        }

        .treble-led {
            bottom: 20px;
            right: 20px;
            background: #4000ff;
            box-shadow: 0 0 15px #4000ff;
            animation-delay: 0.9s;
        }

        .eq-led {
            bottom: 40px;
            left: 40px;
            background: #00ff80;
            box-shadow: 0 0 15px #00ff80;
            animation-delay: 1.1s;
        }

        .radio-led {
            top: 200px;
            left: 150px;
            background: #80ff00;
            box-shadow: 0 0 15px #80ff00;
            animation-delay: 0.4s;
        }

        .cd-led {
            top: 200px;
            right: 150px;
            background: #ff8080;
            box-shadow: 0 0 15px #ff8080;
            animation-delay: 1.3s;
        }

        /* Status bar with multiple LEDs */
        .status-bar {
            position: absolute;
            top: 60px;
            left: 20px;
            width: 200px;
            height: 10px;
            display: flex;
            gap: 5px;
        }

        .status-led {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            animation: led-blink 2s infinite;
        }

        .status-led:nth-child(1) { background: #ff0000; box-shadow: 0 0 10px #ff0000; animation-delay: 0s; }
        .status-led:nth-child(2) { background: #ff4000; box-shadow: 0 0 10px #ff4000; animation-delay: 0.2s; }
        .status-led:nth-child(3) { background: #ff8000; box-shadow: 0 0 10px #ff8000; animation-delay: 0.4s; }
        .status-led:nth-child(4) { background: #ffff00; box-shadow: 0 0 10px #ffff00; animation-delay: 0.6s; }
        .status-led:nth-child(5) { background: #80ff00; box-shadow: 0 0 10px #80ff00; animation-delay: 0.8s; }
        .status-led:nth-child(6) { background: #00ff00; box-shadow: 0 0 10px #00ff00; animation-delay: 1s; }
        .status-led:nth-child(7) { background: #00ff80; box-shadow: 0 0 10px #00ff80; animation-delay: 1.2s; }
        .status-led:nth-child(8) { background: #00ffff; box-shadow: 0 0 10px #00ffff; animation-delay: 1.4s; }
        .status-led:nth-child(9) { background: #0080ff; box-shadow: 0 0 10px #0080ff; animation-delay: 1.6s; }
        .status-led:nth-child(10) { background: #0000ff; box-shadow: 0 0 10px #0000ff; animation-delay: 1.8s; }

        @keyframes led-blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        @keyframes hologram-flicker {
            0%, 100% { opacity: 0.8; }
            25% { opacity: 1; }
            50% { opacity: 0.6; }
            75% { opacity: 0.9; }
        }

        /* Holographic content */
        .holo-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #00ffff;
            font-size: 12px;
            text-shadow: 0 0 10px #00ffff;
            animation: text-glow 2s infinite alternate;
        }

        @keyframes text-glow {
            0% { text-shadow: 0 0 10px #00ffff; }
            100% { text-shadow: 0 0 20px #00ffff, 0 0 30px #00ffff; }
        }

        .equalizer {
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
            height: 100%;
            padding: 10px;
        }

        .eq-bar {
            width: 8px;
            background: linear-gradient(to top, #ff0080, #00ffff);
            border-radius: 2px;
            animation: eq-bounce 1s infinite;
        }

        .eq-bar:nth-child(1) { height: 20%; animation-delay: 0s; }
        .eq-bar:nth-child(2) { height: 60%; animation-delay: 0.1s; }
        .eq-bar:nth-child(3) { height: 80%; animation-delay: 0.2s; }
        .eq-bar:nth-child(4) { height: 40%; animation-delay: 0.3s; }
        .eq-bar:nth-child(5) { height: 90%; animation-delay: 0.4s; }
        .eq-bar:nth-child(6) { height: 30%; animation-delay: 0.5s; }
        .eq-bar:nth-child(7) { height: 70%; animation-delay: 0.6s; }

        @keyframes eq-bounce {
            0%, 100% { transform: scaleY(1); }
            50% { transform: scaleY(1.5); }
        }

        /* Particle effects */
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00ffff;
            border-radius: 50%;
            animation: float 4s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) translateX(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) translateX(100px); opacity: 0; }
        }
    </style>
</head>
<body>
    <div id="container">
        <div class="boombox">
            <!-- Front face -->
            <div class="face front">
                <!-- Antenna -->
                <div class="antenna"></div>

                <!-- Handle -->
                <div class="handle"></div>

                <!-- LED Indicators -->
                <div class="led-indicator power-led"></div>
                <div class="led-indicator record-led"></div>
                <div class="led-indicator bluetooth-led"></div>
                <div class="led-indicator wifi-led"></div>
                <div class="led-indicator aux-led"></div>
                <div class="led-indicator battery-led"></div>
                <div class="led-indicator signal-led"></div>
                <div class="led-indicator stereo-led"></div>
                <div class="led-indicator bass-led"></div>
                <div class="led-indicator treble-led"></div>
                <div class="led-indicator eq-led"></div>
                <div class="led-indicator radio-led"></div>
                <div class="led-indicator cd-led"></div>

                <!-- Status bar with rainbow LEDs -->
                <div class="status-bar">
                    <div class="status-led"></div>
                    <div class="status-led"></div>
                    <div class="status-led"></div>
                    <div class="status-led"></div>
                    <div class="status-led"></div>
                    <div class="status-led"></div>
                    <div class="status-led"></div>
                    <div class="status-led"></div>
                    <div class="status-led"></div>
                    <div class="status-led"></div>
                </div>

                <!-- Main display -->
                <div class="display main-display">
                    <div class="holo-content">
                        <div class="equalizer">
                            <div class="eq-bar"></div>
                            <div class="eq-bar"></div>
                            <div class="eq-bar"></div>
                            <div class="eq-bar"></div>
                            <div class="eq-bar"></div>
                            <div class="eq-bar"></div>
                            <div class="eq-bar"></div>
                        </div>
                    </div>
                </div>

                <!-- Speakers with concentric rings -->
                <div class="display speaker-left">
                    <div class="holo-content">♪ BASS ♪</div>
                    <div style="position: absolute; top: 20px; left: 20px; width: 80px; height: 80px; border: 1px solid #00ffff; border-radius: 50%; opacity: 0.5;"></div>
                    <div style="position: absolute; top: 35px; left: 35px; width: 50px; height: 50px; border: 1px solid #00ffff; border-radius: 50%; opacity: 0.7;"></div>
                    <div style="position: absolute; top: 45px; left: 45px; width: 30px; height: 30px; border: 1px solid #00ffff; border-radius: 50%; opacity: 0.9;"></div>
                </div>
                <div class="display speaker-right">
                    <div class="holo-content">♫ TREBLE ♫</div>
                    <div style="position: absolute; top: 20px; left: 20px; width: 80px; height: 80px; border: 1px solid #00ffff; border-radius: 50%; opacity: 0.5;"></div>
                    <div style="position: absolute; top: 35px; left: 35px; width: 50px; height: 50px; border: 1px solid #00ffff; border-radius: 50%; opacity: 0.7;"></div>
                    <div style="position: absolute; top: 45px; left: 45px; width: 30px; height: 30px; border: 1px solid #00ffff; border-radius: 50%; opacity: 0.9;"></div>
                </div>

                <!-- Control knobs -->
                <div class="knob volume-knob"></div>
                <div class="knob tuning-knob"></div>

                <!-- Control panel -->
                <div class="display control-panel">
                    <div class="holo-content">◄◄ ■ ►► ♪</div>
                </div>

                <!-- Additional small displays -->
                <div class="display" style="width: 60px; height: 30px; top: 160px; left: 100px;">
                    <div class="holo-content" style="font-size: 8px;">FM 101.5</div>
                </div>
                <div class="display" style="width: 60px; height: 30px; top: 160px; right: 100px;">
                    <div class="holo-content" style="font-size: 8px;">VOL 75</div>
                </div>

                <!-- Corner vertices -->
                <div class="vertex" style="top: 0; left: 0;"></div>
                <div class="vertex" style="top: 0; right: 0;"></div>
                <div class="vertex" style="bottom: 0; left: 0;"></div>
                <div class="vertex" style="bottom: 0; right: 0;"></div>

                <!-- Additional wireframe vertices -->
                <div class="vertex" style="top: 0; left: 50%;"></div>
                <div class="vertex" style="bottom: 0; left: 50%;"></div>
                <div class="vertex" style="top: 50%; left: 0;"></div>
                <div class="vertex" style="top: 50%; right: 0;"></div>
                <div class="vertex" style="top: 25%; left: 25%;"></div>
                <div class="vertex" style="top: 25%; right: 25%;"></div>
                <div class="vertex" style="bottom: 25%; left: 25%;"></div>
                <div class="vertex" style="bottom: 25%; right: 25%;"></div>
            </div>
            
            <!-- Back face -->
            <div class="face back">
                <!-- Corner vertices -->
                <div class="vertex" style="top: 0; left: 0;"></div>
                <div class="vertex" style="top: 0; right: 0;"></div>
                <div class="vertex" style="bottom: 0; left: 0;"></div>
                <div class="vertex" style="bottom: 0; right: 0;"></div>
                <!-- Additional vertices -->
                <div class="vertex" style="top: 0; left: 50%;"></div>
                <div class="vertex" style="bottom: 0; left: 50%;"></div>
                <div class="vertex" style="top: 50%; left: 0;"></div>
                <div class="vertex" style="top: 50%; right: 0;"></div>
            </div>

            <!-- Left face -->
            <div class="face left">
                <!-- Corner vertices -->
                <div class="vertex" style="top: 0; left: 0;"></div>
                <div class="vertex" style="top: 0; right: 0;"></div>
                <div class="vertex" style="bottom: 0; left: 0;"></div>
                <div class="vertex" style="bottom: 0; right: 0;"></div>
                <!-- Additional vertices -->
                <div class="vertex" style="top: 0; left: 50%;"></div>
                <div class="vertex" style="bottom: 0; left: 50%;"></div>
                <div class="vertex" style="top: 50%; left: 0;"></div>
                <div class="vertex" style="top: 50%; right: 0;"></div>
                <!-- Side panel details -->
                <div class="display" style="width: 80px; height: 40px; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <div class="holo-content" style="font-size: 10px;">STEREO</div>
                </div>
                <!-- Side LEDs -->
                <div class="led-indicator" style="top: 20px; left: 20px; background: #00ff00; box-shadow: 0 0 15px #00ff00; animation-delay: 0.3s;"></div>
                <div class="led-indicator" style="top: 40px; left: 20px; background: #ff0080; box-shadow: 0 0 15px #ff0080; animation-delay: 0.8s;"></div>
                <div class="led-indicator" style="bottom: 20px; left: 20px; background: #0080ff; box-shadow: 0 0 15px #0080ff; animation-delay: 1.2s;"></div>
            </div>

            <!-- Right face -->
            <div class="face right">
                <!-- Corner vertices -->
                <div class="vertex" style="top: 0; left: 0;"></div>
                <div class="vertex" style="top: 0; right: 0;"></div>
                <div class="vertex" style="bottom: 0; left: 0;"></div>
                <div class="vertex" style="bottom: 0; right: 0;"></div>
                <!-- Additional vertices -->
                <div class="vertex" style="top: 0; left: 50%;"></div>
                <div class="vertex" style="bottom: 0; left: 50%;"></div>
                <div class="vertex" style="top: 50%; left: 0;"></div>
                <div class="vertex" style="top: 50%; right: 0;"></div>
                <!-- Side panel details -->
                <div class="display" style="width: 80px; height: 40px; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                    <div class="holo-content" style="font-size: 10px;">BASS+</div>
                </div>
                <!-- Side LEDs -->
                <div class="led-indicator" style="top: 20px; right: 20px; background: #ff8000; box-shadow: 0 0 15px #ff8000; animation-delay: 0.5s;"></div>
                <div class="led-indicator" style="top: 40px; right: 20px; background: #8000ff; box-shadow: 0 0 15px #8000ff; animation-delay: 1.0s;"></div>
                <div class="led-indicator" style="bottom: 20px; right: 20px; background: #ffff00; box-shadow: 0 0 15px #ffff00; animation-delay: 1.5s;"></div>
            </div>

            <!-- Top face -->
            <div class="face top">
                <!-- Corner vertices -->
                <div class="vertex" style="top: 0; left: 0;"></div>
                <div class="vertex" style="top: 0; right: 0;"></div>
                <div class="vertex" style="bottom: 0; left: 0;"></div>
                <div class="vertex" style="bottom: 0; right: 0;"></div>
                <!-- Additional vertices -->
                <div class="vertex" style="top: 0; left: 33%;"></div>
                <div class="vertex" style="top: 0; left: 66%;"></div>
                <div class="vertex" style="bottom: 0; left: 33%;"></div>
                <div class="vertex" style="bottom: 0; left: 66%;"></div>
                <div class="vertex" style="top: 50%; left: 0;"></div>
                <div class="vertex" style="top: 50%; right: 0;"></div>
                <div class="vertex" style="top: 50%; left: 50%;"></div>
            </div>

            <!-- Bottom face -->
            <div class="face bottom">
                <!-- Corner vertices -->
                <div class="vertex" style="top: 0; left: 0;"></div>
                <div class="vertex" style="top: 0; right: 0;"></div>
                <div class="vertex" style="bottom: 0; left: 0;"></div>
                <div class="vertex" style="bottom: 0; right: 0;"></div>
                <!-- Additional vertices -->
                <div class="vertex" style="top: 0; left: 25%;"></div>
                <div class="vertex" style="top: 0; left: 75%;"></div>
                <div class="vertex" style="bottom: 0; left: 25%;"></div>
                <div class="vertex" style="bottom: 0; left: 75%;"></div>
                <div class="vertex" style="top: 50%; left: 50%;"></div>
            </div>
        </div>
    </div>

    <script>
        // Create floating particles
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + 'vw';
            particle.style.animationDelay = Math.random() * 2 + 's';
            document.body.appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 4000);
        }

        // Generate particles continuously
        setInterval(createParticle, 200);

        // Add interactive rotation control
        let mouseX = 0;
        let mouseY = 0;
        const boombox = document.querySelector('.boombox');

        document.addEventListener('mousemove', (e) => {
            mouseX = (e.clientX / window.innerWidth - 0.5) * 2;
            mouseY = (e.clientY / window.innerHeight - 0.5) * 2;
        });

        function updateRotation() {
            const rotationY = mouseX * 30;
            const rotationX = -mouseY * 15 + 10;
            boombox.style.transform = `rotateY(${rotationY}deg) rotateX(${rotationX}deg)`;
            requestAnimationFrame(updateRotation);
        }

        updateRotation();
    </script>
</body>
</html>
