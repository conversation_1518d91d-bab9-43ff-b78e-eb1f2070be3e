<!DOCTYPE html>
<html>
<head>
    <title>Tesseract Debug</title>
    <style>
        body { margin: 0; background: #000; color: white; font-family: monospace; }
        canvas { display: block; }
        .debug { position: absolute; top: 10px; left: 10px; z-index: 1000; }
    </style>
</head>
<body>
<div class="debug">
    <div id="status">Loading...</div>
    <div id="errors"></div>
</div>
<canvas id="canvas"></canvas>

<script>
const canvas = document.getElementById('canvas');
const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
const status = document.getElementById('status');
const errors = document.getElementById('errors');

if (!gl) {
    status.textContent = 'WebGL not supported';
    throw new Error('WebGL not supported');
}

status.textContent = 'WebGL initialized';

// Resize canvas
function resizeCanvas() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    gl.viewport(0, 0, canvas.width, canvas.height);
}
window.addEventListener('resize', resizeCanvas);
resizeCanvas();

// Simple vertex shader
const vertexShaderSource = `
    attribute vec2 position;
    void main() {
        gl_Position = vec4(position, 0.0, 1.0);
    }
`;

// Simple fragment shader with basic cube
const fragmentShaderSource = `
    precision mediump float;
    
    uniform vec2 resolution;
    uniform float time;
    
    float sdBox(vec3 p, vec3 b) {
        vec3 q = abs(p) - b;
        return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0);
    }
    
    void main() {
        vec2 uv = (gl_FragCoord.xy * 2.0 - resolution) / min(resolution.x, resolution.y);
        
        // Simple camera
        vec3 ro = vec3(0, 0, 3);
        vec3 rd = normalize(vec3(uv, -1));
        
        vec3 col = vec3(0);
        
        // Raymarching
        float t = 0.0;
        for(int i = 0; i < 64; i++) {
            vec3 p = ro + rd * t;
            
            // Rotate the cube
            float c = cos(time);
            float s = sin(time);
            p.xz = mat2(c, -s, s, c) * p.xz;
            
            float d = sdBox(p, vec3(1.0));
            
            if(d < 0.01) {
                col = vec3(0.5 + 0.5 * sin(time), 0.5 + 0.5 * cos(time), 1.0);
                break;
            }
            
            t += d;
            if(t > 10.0) break;
        }
        
        gl_FragColor = vec4(col, 1.0);
    }
`;

// Shader compilation
function createShader(gl, type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        const error = gl.getShaderInfoLog(shader);
        errors.innerHTML += `<div>Shader error: ${error}</div>`;
        gl.deleteShader(shader);
        return null;
    }
    
    return shader;
}

function createProgram(gl, vertexShader, fragmentShader) {
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);
    
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        const error = gl.getProgramInfoLog(program);
        errors.innerHTML += `<div>Program error: ${error}</div>`;
        gl.deleteProgram(program);
        return null;
    }
    
    return program;
}

// Initialize
const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);

if (!vertexShader || !fragmentShader) {
    status.textContent = 'Shader compilation failed';
    throw new Error('Shader compilation failed');
}

const program = createProgram(gl, vertexShader, fragmentShader);

if (!program) {
    status.textContent = 'Program creation failed';
    throw new Error('Program creation failed');
}

status.textContent = 'Shaders compiled successfully';

// Create fullscreen quad
const positions = new Float32Array([
    -1, -1,
     1, -1,
    -1,  1,
     1,  1
]);

const positionBuffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

const positionLocation = gl.getAttribLocation(program, 'position');
gl.enableVertexAttribArray(positionLocation);
gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

// Get uniforms
const resolutionLocation = gl.getUniformLocation(program, 'resolution');
const timeLocation = gl.getUniformLocation(program, 'time');

let startTime = Date.now();

// Render loop
function render() {
    const time = (Date.now() - startTime) * 0.001;
    
    gl.useProgram(program);
    gl.uniform2f(resolutionLocation, canvas.width, canvas.height);
    gl.uniform1f(timeLocation, time);
    
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
    
    requestAnimationFrame(render);
}

status.textContent = 'Starting render loop';
render();
</script>
</body>
</html>
