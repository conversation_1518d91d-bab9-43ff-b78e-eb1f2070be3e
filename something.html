<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Gadgets & Gizmos — Music Visualizer</title>
<style>
  :root{
    --bg:#0b0f16;
    --panel:#0f1720aa;
    --accent:#6ee7b7;
    --muted:#9aa6b2;
    --glass: rgba(255,255,255,0.04);
  }
  html,body{height:100%;margin:0;background:var(--bg);color:#e6eef6;font-family:Inter,ui-sans-serif,system-ui,Helvetica,Arial;}
  .app{display:flex;height:100vh;gap:12px;padding:12px;box-sizing:border-box;}
  .left, .right{background:linear-gradient(180deg, rgba(255,255,255,0.02), transparent);border-radius:12px; padding:12px; box-shadow: 0 8px 30px rgba(0,0,0,0.6); }
  .left{flex:1; display:flex;flex-direction:column; gap:12px; min-width:420px; max-width:900px;}
  .right{width:360px; display:flex; flex-direction:column; gap:12px; overflow:auto;}
  header{display:flex;align-items:center;gap:12px;}
  header h1{margin:0;font-size:18px;letter-spacing:0.4px;}
  .controls{display:flex; gap:8px; align-items:center; flex-wrap:wrap;}
  .btn{background:var(--panel);border:1px solid rgba(255,255,255,0.04);padding:8px 10px;border-radius:8px;color:var(--muted);cursor:pointer;display:inline-flex;gap:8px;align-items:center;}
  .btn.primary{background:linear-gradient(90deg,var(--accent),#60a5fa); color:#021; font-weight:600;border:none;}
  .panel{background:var(--panel);border-radius:10px;padding:10px;box-sizing:border-box;border:1px solid rgba(255,255,255,0.03);}
  label{font-size:12px;color:var(--muted);display:block;margin-bottom:6px;}
  input[type="range"]{width:100%;}
  .row{display:flex;gap:8px;align-items:center;}
  .canvas-wrap{display:grid;grid-template-columns:1fr 1fr;gap:8px; align-items:stretch;}
  canvas{width:100%;height:240px;border-radius:8px;background:transparent;display:block;}
  .big-canvas{height:420px;border-radius:12px;}
  .meter{width:100%;height:30px;border-radius:6px;background:rgba(0,0,0,0.25);overflow:hidden;position:relative;}
  .meter .level{position:absolute;left:0;top:0;bottom:0;width:0%;background:linear-gradient(90deg,var(--accent),#60a5fa);transition:width 0.05s linear;}
  .small{font-size:12px;color:var(--muted);}
  .footer{display:flex;justify-content:space-between;align-items:center;font-size:12px;color:var(--muted);}
  .gadget-title{font-weight:600;font-size:13px;margin-bottom:8px;}
  .control-row{display:flex;gap:8px;align-items:center;}
  .preset{padding:6px 8px;border-radius:8px;background:transparent;border:1px dashed rgba(255,255,255,0.03);cursor:pointer;color:var(--muted);}
  .color-swatch{width:28px;height:28px;border-radius:6px;border:1px solid rgba(255,255,255,0.06);cursor:pointer;}
  .linklike{color:var(--accent);cursor:pointer;}
  .top-controls{display:flex;gap:8px;align-items:center;justify-content:space-between;}
  .logo{width:36px;height:36px;border-radius:9px;background:linear-gradient(135deg,#60a5fa,#6ee7b7);display:flex;align-items:center;justify-content:center;font-weight:700;color:#021;}
  .small-note{font-size:11px;color:var(--muted);}
  /* responsive */
  @media (max-width:980px){
    .app{flex-direction:column;padding:8px;}
    .left{max-width:none;min-width:auto;}
    .right{width:100%;}
    .canvas-wrap{grid-template-columns:1fr;}
    canvas{height:200px;}
    .big-canvas{height:300px;}
  }
</style>
</head>
<body>
<div class="app">
  <div class="left">
    <header>
      <div class="logo">G&G</div>
      <div style="flex:1">
        <h1>Gadgets & Gizmos — Music Visualizer</h1>
        <div class="small-note">Upload a song or let the microphone feed the visuals. Use the controls to tune FFT, sensitivity, and colors.</div>
      </div>
      <div style="display:flex;gap:8px;align-items:center">
        <button class="btn" id="saveImageBtn" title="Save snapshot">📷 Snapshot</button>
        <button class="btn" id="downloadAudioBtn" title="Download processed audio" style="display:none">⬇️ Download</button>
      </div>
    </header>

    <div class="panel canvas-wrap" style="align-items:start;">
      <div style="display:flex;flex-direction:column;gap:8px;">
        <div class="gadget-title">Main Spectrum</div>
        <canvas id="spectrum" class="big-canvas"></canvas>
      </div>

      <div style="display:flex;flex-direction:column;gap:8px;">
        <div class="gadget-title">Radial & Particle Field</div>
        <canvas id="radial" class="big-canvas"></canvas>
      </div>

      <div style="grid-column:1/3;display:flex;gap:8px;">
        <div style="flex:1;" class="panel">
          <div class="gadget-title">Waveform Oscilloscope</div>
          <canvas id="wave" style="height:160px;"></canvas>
        </div>
        <div style="width:300px;" class="panel">
          <div class="gadget-title">VU Meters</div>
          <div class="small">Left</div><div class="meter"><div id="meterL" class="level"></div></div>
          <div class="small">Right</div><div class="meter"><div id="meterR" class="level"></div></div>
          <div style="height:8px"></div>
          <div class="gadget-title">Spectrum Peaks</div>
          <div id="peaks" style="display:flex;gap:6px;"></div>
        </div>
      </div>

    </div>

    <div style="display:flex;justify-content:space-between;gap:8px;">
      <div class="panel" style="flex:1;">
        <div class="gadget-title">Particle Console</div>
        <div class="small-note">Particles react to bass and treble with attraction/repulsion forces.</div>
      </div>
      <div class="panel" style="width:220px;">
        <div class="gadget-title">Reactive Mandala</div>
        <div class="small-note">A hypnotic mandala that pulses with the beat.</div>
      </div>
    </div>

    <div class="footer">
      <div>Built with Web Audio + Canvas — enjoy!</div>
      <div class="small-note">Tip: try larger FFT size for finer spectrum (8192) or smaller for smoother bars (1024).</div>
    </div>
  </div>

  <aside class="right">
    <div class="panel">
      <div class="gadget-title">Input</div>
      <div style="display:flex;gap:8px;align-items:center;">
        <input id="file" type="file" accept="audio/*" />
        <button id="micBtn" class="btn">🎤 Use Microphone</button>
        <button id="stopBtn" class="btn" style="display:none">⏹ Stop Input</button>
      </div>
      <div style="height:8px"></div>
      <div id="trackInfo" class="small-note"></div>
      <div style="height:6px"></div>
      <div style="display:flex;gap:8px;align-items:center;">
        <button id="playBtn" class="btn primary">▶ Play</button>
        <button id="pauseBtn" class="btn" style="display:none">⏸ Pause</button>
        <button id="muteBtn" class="btn">🔇 Mute</button>
      </div>
    </div>

    <div class="panel">
      <div class="gadget-title">Analyzer Settings</div>
      <label>FFT Size: <span id="fftLabel">2048</span></label>
      <input id="fft" type="range" min="512" max="16384" step="2" value="2048" />
      <label>Sensitivity: <span id="sensLabel">1.0</span></label>
      <input id="sensitivity" type="range" min="0.1" max="4" step="0.01" value="1" />
      <label>Smoothing (0-0.99): <span id="smoothLabel">0.8</span></label>
      <input id="smoothing" type="range" min="0" max="0.99" step="0.01" value="0.8" />
    </div>

    <div class="panel">
      <div class="gadget-title">Appearance</div>
      <label>Accent Color</label>
      <input id="accentColor" type="color" value="#6ee7b7" />
      <label>Background Darkness</label>
      <input id="bgDark" type="range" min="0" max="1" step="0.01" value="0.92" />
      <label>Show grid</label>
      <input id="showGrid" type="checkbox" checked />
      <div style="height:8px"></div>
      <div class="gadget-title">Presets</div>
      <div style="display:flex;gap:6px;flex-wrap:wrap;">
        <button class="preset" data-preset="neon">Neon</button>
        <button class="preset" data-preset="calm">Calm</button>
        <button class="preset" data-preset="hot">Fire</button>
        <button class="preset" data-preset="mono">Monochrome</button>
      </div>
    </div>

    <div class="panel">
      <div class="gadget-title">Diagnostics</div>
      <div class="small-note">Frame: <span id="fps">0</span> | Samples: <span id="sampleRate">0</span></div>
      <div style="height:8px"></div>
      <div class="small-note">Latency sensitive: avoid background tabs for best realtime mic input.</div>
    </div>

    <div class="panel" style="text-align:center;">
      <div class="small-note">Made for tinkering ✨</div>
      <div style="height:6px"></div>
      <div class="small-note"><a class="linklike" id="resetBtn">Reset Settings</a></div>
    </div>
  </aside>
</div>

<script>
/*
  Gadgets & Gizmos Visualizer (single-file)
  - Accepts audio file input or microphone stream
  - Multiple canvases synchronized via WebAudio Analyser
  - Controls: FFT size, sensitivity, smoothing, color preset
  - Notes: This is an educational, tweakable demo. Browser must allow microphone access for mic usage.
*/

(async function(){
  // DOM
  const fileInput = document.getElementById('file');
  const playBtn = document.getElementById('playBtn');
  const pauseBtn = document.getElementById('pauseBtn');
  const muteBtn = document.getElementById('muteBtn');
  const micBtn = document.getElementById('micBtn');
  const stopBtn = document.getElementById('stopBtn');
  const saveImageBtn = document.getElementById('saveImageBtn');

  const fftSlider = document.getElementById('fft');
  const fftLabel  = document.getElementById('fftLabel');
  const sensSlider= document.getElementById('sensitivity');
  const sensLabel = document.getElementById('sensLabel');
  const smoothSlider = document.getElementById('smoothing');
  const smoothLabel  = document.getElementById('smoothLabel');
  const accentColor = document.getElementById('accentColor');
  const bgDark = document.getElementById('bgDark');
  const showGrid = document.getElementById('showGrid');
  const presetButtons = document.querySelectorAll('.preset');
  const resetBtn = document.getElementById('resetBtn');

  const fpsLabel = document.getElementById('fps');
  const sampleRateLabel = document.getElementById('sampleRate');

  const spectrumCanvas = document.getElementById('spectrum');
  const radialCanvas = document.getElementById('radial');
  const waveCanvas = document.getElementById('wave');
  const meterL = document.getElementById('meterL');
  const meterR = document.getElementById('meterR');
  const peaksEl = document.getElementById('peaks');
  const trackInfo = document.getElementById('trackInfo');

  // Canvas contexts & size helpers
  const ctxSpec = spectrumCanvas.getContext('2d', {alpha:true});
  const ctxRad = radialCanvas.getContext('2d', {alpha:true});
  const ctxWave = waveCanvas.getContext('2d', {alpha:true});

  function resizeCanvases(){
    const dpr = window.devicePixelRatio || 1;
    [spectrumCanvas, radialCanvas, waveCanvas].forEach(c=>{
      const rect = c.getBoundingClientRect();
      c.width = Math.max(300, Math.floor(rect.width * dpr));
      c.height = Math.max(160, Math.floor(rect.height * dpr));
    });
  }
  window.addEventListener('resize', resizeCanvases);
  resizeCanvases();

  // Audio setup
  let audioCtx = null;
  let sourceNode = null;
  let analyser = null;
  let gainNode = null;
  let audioBuffer = null;
  let mediaStream = null;
  let mediaSource = null;
  let isPlaying = false;
  let isMuted = false;

  function ensureAudio(){
    if(!audioCtx){
      audioCtx = new (window.AudioContext || window.webkitAudioContext)();
      sampleRateLabel.textContent = audioCtx.sampleRate;
    }
  }

  function createAnalyser(fftSize = 2048, smoothing = 0.8){
    if(!audioCtx) ensureAudio();
    analyser = audioCtx.createAnalyser();
    analyser.fftSize = fftSize;
    analyser.smoothingTimeConstant = smoothing;
    gainNode = audioCtx.createGain();
    gainNode.gain.value = 1;
    analyser.connect(gainNode);
    gainNode.connect(audioCtx.destination);
    return analyser;
  }

  // Audio element for file playback
  const audioEl = new Audio();
  audioEl.crossOrigin = 'anonymous';
  audioEl.loop = true;
  audioEl.addEventListener('ended', ()=>{ isPlaying=false; updatePlayButtons(); });

  // connect audio element to context
  function connectAudioElement(){
    if(!audioCtx) ensureAudio();
    if(sourceNode) try{ sourceNode.disconnect(); }catch(e){}
    sourceNode = audioCtx.createMediaElementSource(audioEl);
    if(!analyser) createAnalyser(parseInt(fftSlider.value), parseFloat(smoothSlider.value));
    sourceNode.connect(analyser);
    analyser.connect(gainNode);
  }

  // handle file input
  fileInput.addEventListener('change', async (ev)=>{
    const f = ev.target.files && ev.target.files[0];
    if(!f) return;
    trackInfo.textContent = `Loaded: ${f.name} (${Math.round(f.size/1024)} KB)`;
    // use object URL for audio tag
    audioEl.src = URL.createObjectURL(f);
    audioEl.oncanplay = ()=>{
      try{
        // create audio context and connect
        ensureAudio();
        if(!analyser) createAnalyser(parseInt(fftSlider.value), parseFloat(smoothSlider.value));
        connectAudioElement();
        audioEl.play();
        isPlaying = true;
        updatePlayButtons();
      }catch(e){
        console.error(e);
      }
    };
  });

  // microphone
  micBtn.addEventListener('click', async ()=>{
    try{
      if(!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia){
        alert('Microphone not supported in this browser.');
        return;
      }
      ensureAudio();
      mediaStream = await navigator.mediaDevices.getUserMedia({audio:{channelCount:2, echoCancellation:true}});
      mediaSource = audioCtx.createMediaStreamSource(mediaStream);
      if(!analyser) createAnalyser(parseInt(fftSlider.value), parseFloat(smoothSlider.value));
      mediaSource.connect(analyser);
      isPlaying = true;
      updatePlayButtons();
      micBtn.style.display='none';
      stopBtn.style.display='inline-flex';
      trackInfo.textContent = `Microphone active`;
    }catch(err){
      console.error(err);
      alert('Microphone access denied or unavailable.');
    }
  });

  stopBtn.addEventListener('click', ()=>{
    if(mediaStream){
      mediaStream.getTracks().forEach(t=>t.stop());
      mediaStream = null;
      if(mediaSource) { try{ mediaSource.disconnect(); }catch(e){} mediaSource=null; }
      micBtn.style.display='inline-flex';
      stopBtn.style.display='none';
      trackInfo.textContent = '';
      isPlaying = false;
      updatePlayButtons();
    }
  });

  // play/pause/mute
  playBtn.addEventListener('click', async ()=>{
    if(audioCtx && audioCtx.state === 'suspended'){ await audioCtx.resume(); }
    if(audioEl.src){
      try{
        await audioEl.play();
        isPlaying = true;
        connectAudioElement();
      }catch(e){ console.warn(e); }
    } else {
      // if no file, try resume mic or do nothing
      if(mediaStream) isPlaying = true;
    }
    updatePlayButtons();
  });
  pauseBtn.addEventListener('click', ()=>{
    if(audioEl && !audioEl.paused){ audioEl.pause(); isPlaying=false; updatePlayButtons(); }
  });
  muteBtn.addEventListener('click', ()=>{
    isMuted = !isMuted;
    if(gainNode) gainNode.gain.value = isMuted ? 0 : 1;
    muteBtn.textContent = isMuted ? '🔈 Unmute' : '🔇 Mute';
  });

  function updatePlayButtons(){
    if(isPlaying){
      playBtn.style.display='none';
      pauseBtn.style.display='inline-flex';
    } else {
      playBtn.style.display='inline-flex';
      pauseBtn.style.display='none';
    }
  }

  // analyser settings controls
  fftSlider.addEventListener('input', ()=>{
    const val = parseInt(fftSlider.value);
    fftLabel.textContent = val;
    if(analyser){
      // must be power-of-two; clamp to nearest pow2
      const pow2 = Math.pow(2, Math.round(Math.log2(val)));
      analyser.fftSize = pow2;
    }
  });
  sensSlider.addEventListener('input', ()=>{ sensLabel.textContent = parseFloat(sensSlider.value).toFixed(2); });
  smoothSlider.addEventListener('input', ()=>{ smoothLabel.textContent = parseFloat(smoothSlider.value).toFixed(2); if(analyser) analyser.smoothingTimeConstant = parseFloat(smoothSlider.value); });

  // presets
  const presetMap = {
    neon: {accent:'#6ee7b7', bg:0.92},
    calm: {accent:'#60a5fa', bg:0.98},
    hot: {accent:'#ff6b6b', bg:0.85},
    mono: {accent:'#bcbcbc', bg:0.95}
  };
  presetButtons.forEach(b=>{
    b.addEventListener('click', ()=>{
      const p = b.dataset.preset;
      const cfg = presetMap[p];
      if(cfg){
        accentColor.value = cfg.accent;
        bgDark.value = cfg.bg;
        applyAppearance();
      }
    });
  });
  resetBtn.addEventListener('click', ()=>{
    accentColor.value = '#6ee7b7';
    bgDark.value = 0.92;
    fftSlider.value = 2048; fftLabel.textContent = 2048;
    sensSlider.value = 1; sensLabel.textContent='1.00';
    smoothSlider.value = 0.8; smoothLabel.textContent='0.80';
    showGrid.checked = true;
    applyAppearance();
  });

  function applyAppearance(){
    const accent = accentColor.value;
    document.documentElement.style.setProperty('--accent', accent);
    const bg = parseFloat(bgDark.value);
    // compute a dark color based on bg slider
    const dark = `rgba(5,8,12,${bg})`;
    document.body.style.background = dark;
  }
  accentColor.addEventListener('input', applyAppearance);
  bgDark.addEventListener('input', applyAppearance);
  showGrid.addEventListener('change', ()=>{ /* used in draw functions */ });
  applyAppearance();

  // Utility: create array of peaks indicators
  function initPeaks(n=8){
    peaksEl.innerHTML='';
    for(let i=0;i<n;i++){
      const el = document.createElement('div');
      el.style.flex='1';
      el.style.height='8px';
      el.style.background='linear-gradient(90deg,var(--accent),#60a5fa)';
      el.style.opacity='0.18';
      el.style.borderRadius='4px';
      peaksEl.appendChild(el);
    }
  }
  initPeaks(8);

  // Visualization state
  let lastTime = performance.now();
  let frameCount = 0;
  let fps = 0;

  // Particle system
  const particles = [];
  function initParticles(n=120){
    particles.length = 0;
    for(let i=0;i<n;i++){
      particles.push({
        x: Math.random(),
        y: Math.random(),
        vx: (Math.random()-0.5)*0.002,
        vy: (Math.random()-0.5)*0.002,
        size: Math.random()*2+0.5,
        hue: Math.random()*360
      });
    }
  }
  initParticles(140);

  // Mandala state
  let mandalaPhase = 0;

  // drawing helpers
  function drawGrid(ctx,w,h,spacing=40,alpha=0.03){
    if(!showGrid.checked) return;
    ctx.save();
    ctx.strokeStyle = `rgba(255,255,255,${alpha})`;
    ctx.lineWidth = 1;
    for(let x=0;x<w;x+=spacing){
      ctx.beginPath(); ctx.moveTo(x,0); ctx.lineTo(x,h); ctx.stroke();
    }
    for(let y=0;y<h;y+=spacing){
      ctx.beginPath(); ctx.moveTo(0,y); ctx.lineTo(w,y); ctx.stroke();
    }
    ctx.restore();
  }

  // main render loop
  function draw(){
    if(!analyser){
      requestAnimationFrame(draw);
      return;
    }

    const now = performance.now();
    frameCount++;
    if(now - lastTime >= 1000){
      fps = frameCount;
      fpsLabel.textContent = fps;
      frameCount = 0;
      lastTime = now;
    }

    // get data
    const fftSize = analyser.fftSize;
    const bufferLength = fftSize/2;
    const data = new Uint8Array(bufferLength);
    analyser.getByteFrequencyData(data);
    const timeData = new Uint8Array(analyser.fftSize);
    analyser.getByteTimeDomainData(timeData);

    const wSpec = spectrumCanvas.width;
    const hSpec = spectrumCanvas.height;
    const wRad = radialCanvas.width;
    const hRad = radialCanvas.height;
    const wWave = waveCanvas.width;
    const hWave = waveCanvas.height;

    // common parameters
    const sensitivity = parseFloat(sensSlider.value);
    const accent = accentColor.value;

    // SPECTRUM canvas (bars + gradient + glow)
    ctxSpec.clearRect(0,0,wSpec,hSpec);
    // background glow
    ctxSpec.fillStyle = 'rgba(0,0,0,0.1)';
    ctxSpec.fillRect(0,0,wSpec,hSpec);
    drawGrid(ctxSpec, wSpec, hSpec, Math.max(40, Math.floor(wSpec/16)), 0.02);

    const barCount = Math.min(150, bufferLength);
    const barW = wSpec / barCount;
    // create gradient
    const grad = ctxSpec.createLinearGradient(0,0,0,hSpec);
    grad.addColorStop(0, accent);
    grad.addColorStop(0.5, '#8b5cf6');
    grad.addColorStop(1, '#ef476f');
    ctxSpec.fillStyle = grad;
    for(let i=0;i<barCount;i++){
      const v = data[i] / 255;
      const h = Math.pow(v, 1.2) * hSpec * 0.95 * sensitivity;
      const x = Math.floor(i * barW);
      // bar shadow/glow
      ctxSpec.globalAlpha = 0.12;
      ctxSpec.fillRect(x, hSpec - h - 6, barW*0.9, h+6);
      ctxSpec.globalAlpha = 1;
      ctxSpec.fillRect(x, hSpec - h, barW*0.8, h);
    }
    // spectrum peak overlay (thin lines)
    ctxSpec.save();
    ctxSpec.globalCompositeOperation = 'lighter';
    ctxSpec.strokeStyle = accent;
    ctxSpec.lineWidth = 1.2;
    ctxSpec.beginPath();
    for(let i=0;i<barCount;i++){
      const v = data[i] / 255;
      const h = Math.pow(v,1.25) * hSpec * 0.95 * sensitivity;
      const x = i * barW + barW*0.3;
      if(i===0) ctxSpec.moveTo(x, hSpec - h);
      else ctxSpec.lineTo(x, hSpec - h);
    }
    ctxSpec.stroke();
    ctxSpec.restore();

    // RADIAL canvas (circular spectrum + particles)
    ctxRad.clearRect(0,0,wRad,hRad);
    // background circle
    ctxRad.save();
    ctxRad.translate(wRad/2, hRad/2);
    const R = Math.min(wRad,hRad)/3;
    const radialCount = 120;
    ctxRad.globalCompositeOperation = 'lighter';
    for(let i=0;i<radialCount;i++){
      const idx = Math.floor(i / radialCount * bufferLength);
      const v = data[idx] / 255;
      const ang = i / radialCount * Math.PI * 2 + mandalaPhase*0.001;
      const len = R + Math.pow(v,1.2) * R * 0.9 * sensitivity;
      const x1 = Math.cos(ang) * R * 0.3;
      const y1 = Math.sin(ang) * R * 0.3;
      const x2 = Math.cos(ang) * len;
      const y2 = Math.sin(ang) * len;
      ctxRad.strokeStyle = `hsla(${i/ radialCount*360},70%,60%,0.14)`;
      ctxRad.lineWidth = 2;
      ctxRad.beginPath();
      ctxRad.moveTo(x1,y1);
      ctxRad.lineTo(x2,y2);
      ctxRad.stroke();
    }

    // particles (react to bass & treble)
    const bass = Math.max(...data.slice(0, Math.max(3, Math.floor(bufferLength*0.06))))/255;
    const treble = Math.max(...data.slice(Math.floor(bufferLength*0.6), bufferLength))/255;

    // update particles
    for(let p of particles){
      // convert normalized to canvas coords:
      const px = (p.x - 0.5) * wRad/1.1;
      const py = (p.y - 0.5) * hRad/1.1;
      // attract to center when bass high, repel when treble high
      const cx = 0, cy = 0;
      const ax = (cx - px) * (0.00005 + 0.0003 * bass);
      const ay = (cy - py) * (0.00005 + 0.0003 * bass);
      p.vx += ax - (Math.random()-0.5)*0.00002 * treble;
      p.vy += ay - (Math.random()-0.5)*0.00002 * treble;
      p.vx *= 0.995;
      p.vy *= 0.995;
      p.x += p.vx * (1 + bass*2);
      p.y += p.vy * (1 + bass*2);
      // wrap
      if(p.x < -0.5) p.x = 1.5;
      if(p.x > 1.5) p.x = -0.5;
      if(p.y < -0.5) p.y = 1.5;
      if(p.y > 1.5) p.y = -0.5;
      // draw
      ctxRad.beginPath();
      ctxRad.fillStyle = `hsla(${Math.floor(p.hue + bass*120)}, 70%, 60%, ${0.9})`;
      ctxRad.arc((p.x-0.5)*wRad/1.1, (p.y-0.5)*hRad/1.1, p.size*(1+bass*3), 0, Math.PI*2);
      ctxRad.fill();
    }

    // mandala center rings
    ctxRad.beginPath();
    const rings = 6;
    for(let r=0;r<rings;r++){
      const rr = R*0.2 + r*(R*0.12) + Math.sin(mandalaPhase*0.003 + r)*10*(bass+0.1);
      ctxRad.strokeStyle = `rgba(255,255,255,${0.04 + r*0.03})`;
      ctxRad.lineWidth = 1;
      ctxRad.beginPath();
      ctxRad.arc(0,0, rr, 0, Math.PI*2);
      ctxRad.stroke();
    }

    mandalaPhase += 1 + bass*6;

    ctxRad.restore();

    // WAVEFORM canvas (oscilloscope)
    ctxWave.clearRect(0,0,wWave,hWave);
    // draw time domain waveform
    ctxWave.save();
    ctxWave.globalCompositeOperation = 'lighter';
    ctxWave.lineWidth = 2;
    ctxWave.beginPath();
    const step = Math.ceil(timeData.length / wWave);
    for(let i=0, x=0; x<wWave && i<timeData.length; i+=step, x++){
      const v = (timeData[i] - 128) / 128;
      const y = hWave/2 + v * hWave/2 * sensitivity * 0.9;
      if(x===0) ctxWave.moveTo(x,y);
      else ctxWave.lineTo(x,y);
    }
    ctxWave.strokeStyle = accent;
    ctxWave.stroke();
    // fade trail
    ctxWave.globalAlpha = 0.08;
    ctxWave.fillStyle = accent;
    ctxWave.fillRect(0,0,wWave,hWave);
    ctxWave.restore();

    // VU Meters & peaks
    const leftLevel = Math.max(...data.slice(0, Math.floor(bufferLength/8)))/255;
    const rightLevel = Math.max(...data.slice(Math.floor(bufferLength/2), Math.floor(bufferLength/2 + bufferLength/8)))/255;
    meterL.style.width = `${Math.min(100, Math.pow(leftLevel*sensitivity,1)*100)}%`;
    meterR.style.width = `${Math.min(100, Math.pow(rightLevel*sensitivity,1)*100)}%`;

    // peaks: show big bins
    const peakEls = peaksEl.children;
    for(let i=0;i<peakEls.length;i++){
      const idx = Math.floor(i/peakEls.length * bufferLength * 0.9);
      const value = data[idx]/255;
      peakEls[i].style.opacity = 0.12 + Math.pow(value*sensitivity,1.2)*0.88;
      peakEls[i].style.transform = `scaleY(${0.6 + value*2})`;
      peakEls[i].style.transition = 'opacity 0.06s linear, transform 0.06s linear';
    }

    // slight FPS friendly delay handled by rAF naturally
    requestAnimationFrame(draw);
  }

  // Initialization of audio pipeline (create analyser on first user interaction)
  function initOnDemand(){
    if(!audioCtx) ensureAudio();
    if(!analyser) createAnalyser(parseInt(fftSlider.value), parseFloat(smoothSlider.value));
    // always start draw loop
    if(!window._visualizerRunning){
      draw();
      window._visualizerRunning = true;
    }
  }
  // user gesture to allow AudioContext on some browsers
  document.addEventListener('click', initOnDemand, {once:true});
  document.addEventListener('keydown', initOnDemand, {once:true});

  // snapshot
  saveImageBtn.addEventListener('click', ()=>{
    // merge canvases into one image
    const w = Math.max(spectrumCanvas.width, radialCanvas.width);
    const h = spectrumCanvas.height + radialCanvas.height + waveCanvas.height;
    const tmp = document.createElement('canvas');
    const dpr = window.devicePixelRatio || 1;
    tmp.width = w;
    tmp.height = h;
    const tctx = tmp.getContext('2d');
    // draw background
    tctx.fillStyle = getComputedStyle(document.body).background || '#000';
    tctx.fillRect(0,0,w,h);
    // draw spectrum
    tctx.drawImage(spectrumCanvas, 0, 0, spectrumCanvas.width, spectrumCanvas.height, 0, 0, w, spectrumCanvas.height);
    // draw radial
    tctx.drawImage(radialCanvas, 0, 0, radialCanvas.width, radialCanvas.height, 0, spectrumCanvas.height, w, radialCanvas.height);
    // draw wave
    tctx.drawImage(waveCanvas, 0, 0, waveCanvas.width, waveCanvas.height, 0, spectrumCanvas.height + radialCanvas.height, w, waveCanvas.height);

    const link = document.createElement('a');
    link.download = 'visualizer_snapshot.png';
    link.href = tmp.toDataURL('image/png');
    link.click();
  });

  // initialize some things and start draw loop when possible
  initOnDemand();
  window.addEventListener('resize', resizeCanvases);

  // start rAF
  requestAnimationFrame(draw);

  // nice little autoplay friendly helper: attempt to resume audio context on interaction
  document.addEventListener('visibilitychange', ()=>{ if(document.visibilityState === 'visible' && audioCtx && audioCtx.state === 'suspended'){ audioCtx.resume(); }});

  // small UI polish: show sampleRate
  if(window.AudioContext) {
    ensureAudio();
    sampleRateLabel.textContent = audioCtx.sampleRate;
  }

  // simple instructions on load
  trackInfo.textContent = 'Upload a track or use the microphone. Click Play to start.';

  // EOF
})();
</script>
</body>
</html>
