<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>All‑in‑One Music Visualizers (10 styles)</title>
<style>
  :root{
    --fg:#aefcff; --accent:#00e5ff; --bg:#06080a; --panel:#0b1116; --muted:#6aa4b1;
  }
  html,body{height:100%;}
  body{margin:0;background:radial-gradient(1200px 1200px at 70% 20%, #0b1318 0%, #071017 40%, var(--bg) 100%);color:var(--fg);font:14px/1.4 ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;}
  .app{display:grid;grid-template-rows:auto 1fr auto;min-height:100vh}

  header{display:flex;gap:.75rem;flex-wrap:wrap;align-items:center;justify-content:space-between;padding:10px 14px;background:linear-gradient(180deg,#0e151c,#0a1218);border-bottom:1px solid #0f2027;box-shadow:0 1px 0 #09141a inset}
  header .left, header .right{display:flex;gap:.5rem;flex-wrap:wrap;align-items:center}
  h1{font-size:14px;margin:0;letter-spacing:.08em;text-transform:uppercase;color:#c4f4ff}

  .control{background:var(--panel);border:1px solid #123;box-shadow:0 0 0 1px #07151b inset,0 0 12px rgba(0,229,255,.08);padding:8px 10px;border-radius:10px;display:flex;align-items:center;gap:.5rem}
  .control label{font-size:12px;color:var(--muted)}
  select,input[type="range"]{accent-color:var(--accent)}
  button{cursor:pointer;border:1px solid #0d2a34;background:#0a1820;color:var(--fg);padding:8px 10px;border-radius:10px}
  button:hover{background:#0d2029}

  .stage{position:relative;overflow:hidden}
  canvas{width:100%;height:100%;display:block}
  .overlay{position:absolute;inset:auto 12px 12px auto;background:rgba(6,12,16,.55);backdrop-filter:blur(4px);border:1px solid #0c2a33;border-radius:10px;padding:8px 10px;box-shadow:0 0 12px rgba(0,229,255,.08);}
  .overlay small{display:block;color:#7ecdd9}

  footer{display:flex;gap:.5rem;flex-wrap:wrap;align-items:center;justify-content:center;padding:8px;background:linear-gradient(0deg,#0e151c,#0a1218);border-top:1px solid #0f2027}
  .kbd{border:1px solid #16323b;background:#0a1b21;border-radius:6px;padding:2px 6px;color:#9fe2f0}

  /* tiny responsive tweaks */
  @media (max-width:700px){
    header{gap:.5rem}
    h1{width:100%}
  }
</style>
</head>
<body>
  <div class="app">
    <header>
      <div class="left">
        <h1>Music Visualizers ×10</h1>
        <div class="control">
          <label for="vizSelect">Visualizer</label>
          <select id="vizSelect" title="Choose visualizer style">
            <option value="bars">1) Classic Equalizer Bars</option>
            <option value="circleWave">2) Circular Audio Wave</option>
            <option value="radialSpectrum">3) Rotating Radial Spectrum</option>
            <option value="dotMatrix">4) Dot Matrix</option>
            <option value="radialBurst">5) Radial Burst Spikes</option>
            <option value="oscilloscope">6) Wavy Oscilloscope Line</option>
            <option value="polyPulse">7) Geometric Polygon Pulse</option>
            <option value="particlePulse">8) Particle Pulse</option>
            <option value="gridHeatmap">9) Grid Heatmap</option>
            <option value="spectrumTunnel">10) Spectrum Tunnel</option>
          </select>
        </div>
        <div class="control">
          <label for="fft">FFT</label>
          <input id="fft" type="range" min="5" max="12" step="1" value="10" />
        </div>
        <div class="control">
          <label for="smoothing">Smoothing</label>
          <input id="smoothing" type="range" min="0" max="0.95" step="0.05" value="0.6" />
        </div>
      </div>
      <div class="right">
        <div class="control">
          <input id="file" type="file" accept="audio/*" />
          <button id="micBtn" title="Use microphone">🎙️ Mic</button>
          <button id="playPause">▶️ Play</button>
          <label for="vol">Vol</label>
          <input id="vol" type="range" min="0" max="1" step="0.01" value="0.9" />
        </div>
      </div>
    </header>

    <div class="stage">
      <canvas id="canvas"></canvas>
      <div class="overlay"><small id="readout">No source loaded — use File or Mic</small></div>
    </div>

    <footer>
      <span class="kbd">File</span> load an audio file • <span class="kbd">🎙️ Mic</span> live input • <span class="kbd">FFT</span> detail • <span class="kbd">Smoothing</span> averaging
    </footer>
  </div>

<script>
(() => {
  const dpr = Math.max(1, Math.min(2, window.devicePixelRatio || 1));
  const canvas = document.getElementById('canvas');
  const ctx = canvas.getContext('2d');
  const vizSelect = document.getElementById('vizSelect');
  const fileInput = document.getElementById('file');
  const micBtn = document.getElementById('micBtn');
  const playPauseBtn = document.getElementById('playPause');
  const volSlider = document.getElementById('vol');
  const fftSlider = document.getElementById('fft');
  const smoothingSlider = document.getElementById('smoothing');
  const readout = document.getElementById('readout');

  let audioCtx, analyser, sourceNode, gainNode;
  let mediaEl = new Audio();
  mediaEl.crossOrigin = 'anonymous';
  mediaEl.loop = true;
  mediaEl.preload = 'auto';

  let usingMic = false;
  let particles = [];

  function ensureAudioGraph(){
    if (!audioCtx){
      audioCtx = new (window.AudioContext || window.webkitAudioContext)();
      analyser = audioCtx.createAnalyser();
      gainNode = audioCtx.createGain();
      analyser.fftSize = 1 << parseInt(fftSlider.value,10); // 2^N
      analyser.smoothingTimeConstant = parseFloat(smoothingSlider.value);
      gainNode.gain.value = parseFloat(volSlider.value);
    }
  }

  function connectMediaElement(){
    ensureAudioGraph();
    if (sourceNode) try{ sourceNode.disconnect(); }catch(_){}
    sourceNode = audioCtx.createMediaElementSource(mediaEl);
    sourceNode.connect(analyser);
    analyser.connect(gainNode);
    gainNode.connect(audioCtx.destination);
    usingMic = false;
    readout.textContent = 'Source: File';
  }

  async function connectMic(){
    ensureAudioGraph();
    try{
      const stream = await navigator.mediaDevices.getUserMedia({audio:true});
      if (sourceNode) try{ sourceNode.disconnect(); }catch(_){}
      sourceNode = audioCtx.createMediaStreamSource(stream);
      sourceNode.connect(analyser);
      analyser.connect(gainNode);
      gainNode.connect(audioCtx.destination);
      usingMic = true;
      mediaEl.pause();
      readout.textContent = 'Source: Microphone (live)';
    }catch(err){
      console.error(err);
      alert('Microphone permission denied or unavailable.');
    }
  }

  fileInput.addEventListener('change', e => {
    const file = e.target.files[0];
    if (!file) return;
    const url = URL.createObjectURL(file);
    connectMediaElement();
    mediaEl.src = url;
    mediaEl.play();
    playPauseBtn.textContent = '⏸️ Pause';
  });

  micBtn.addEventListener('click', () => {
    connectMic();
    playPauseBtn.textContent = '⏸️ Live';
  });

  playPauseBtn.addEventListener('click', async () => {
    ensureAudioGraph();
    await audioCtx.resume();
    if (usingMic){
      // toggle monitoring by changing gain to 0/vol
      if (gainNode.gain.value > 0){ gainNode.gain.value = 0; playPauseBtn.textContent = '▶️ Monitor'; }
      else { gainNode.gain.value = parseFloat(volSlider.value); playPauseBtn.textContent = '⏸️ Live'; }
    } else {
      if (mediaEl.paused){ mediaEl.play(); playPauseBtn.textContent = '⏸️ Pause'; }
      else { mediaEl.pause(); playPauseBtn.textContent = '▶️ Play'; }
    }
  });

  volSlider.addEventListener('input', e => { if (gainNode) gainNode.gain.value = parseFloat(e.target.value); });
  fftSlider.addEventListener('input', e => { if (analyser){ analyser.fftSize = 1 << parseInt(e.target.value,10); allocBuffers(); }});
  smoothingSlider.addEventListener('input', e => { if (analyser){ analyser.smoothingTimeConstant = parseFloat(e.target.value); }});

  let freqData, timeData;
  function allocBuffers(){
    if (!analyser) return;
    freqData = new Uint8Array(analyser.frequencyBinCount);
    timeData = new Uint8Array(analyser.fftSize);
  }

  function sizeCanvas(){
    const {clientWidth:w, clientHeight:h} = canvas;
    canvas.width = Math.floor(w * dpr);
    canvas.height = Math.floor(h * dpr);
    ctx.setTransform(dpr,0,0,dpr,0,0);
  }
  window.addEventListener('resize', sizeCanvas);
  sizeCanvas();
  ensureAudioGraph();
  allocBuffers();

  // Utility helpers
  const TAU = Math.PI * 2;
  function lerp(a,b,t){return a + (b-a)*t}
  function clamp(v,min,max){return Math.max(min, Math.min(max, v))}
  function avg(arr,start=0,end=arr.length){
    let s=0,n=0; for(let i=start;i<end;i++){ s+=arr[i]; n++; } return n? s/n : 0;
  }
  function bassLevel(){
    if (!freqData) return 0; const n = Math.max(8, Math.floor(freqData.length*0.05));
    return avg(freqData, 0, n) / 255;
  }

  // ---------- Visualizers ----------
  const visualizers = {
    // 1) Classic bars
    bars(){
      const w = canvas.clientWidth, h = canvas.clientHeight;
      const bins = freqData.length;
      const barW = Math.max(2, (w / bins) * 1.6);
      const gap = Math.max(0, (w - barW*bins) / bins);
      ctx.clearRect(0,0,w,h);
      const g = ctx.createLinearGradient(0,0,0,h);
      g.addColorStop(0,'#aff'); g.addColorStop(1,'#08a');
      ctx.fillStyle = g;
      for(let i=0;i<bins;i++){
        const v = freqData[i]/255;
        const x = i*(barW+gap);
        const y = h * (1 - Math.pow(v,1.2));
        ctx.fillRect(x, y, barW, h - y);
      }
      // glow top line
      ctx.strokeStyle = 'rgba(0,229,255,.35)'; ctx.lineWidth = 2; ctx.beginPath();
      for(let i=0;i<bins;i++){
        const v = freqData[i]/255;
        const x = i*(barW+gap) + barW/2; const y = h * (1 - Math.pow(v,1.2));
        if(i===0) ctx.moveTo(x,y); else ctx.lineTo(x,y);
      }
      ctx.stroke();
    },

    // 2) Circular waveform
    circleWave(){
      const w = canvas.clientWidth, h = canvas.clientHeight; ctx.clearRect(0,0,w,h);
      const cx = w/2, cy = h/2; const baseR = Math.min(w,h)*0.22; const scale = Math.min(w,h)*0.18;
      ctx.translate(cx,cy);
      ctx.rotate(Date.now()/3000);
      ctx.beginPath();
      for(let i=0;i<timeData.length;i++){
        const t = i/timeData.length; const ang = t*TAU;
        const v = (timeData[i]-128)/128; // -1..1
        const r = baseR + v*scale;
        const x = Math.cos(ang)*r, y = Math.sin(ang)*r;
        if(i===0) ctx.moveTo(x,y); else ctx.lineTo(x,y);
      }
      ctx.closePath();
      ctx.strokeStyle = '#aefcff'; ctx.lineWidth = 2; ctx.stroke();
      // halo
      ctx.strokeStyle = 'rgba(0,229,255,.25)'; ctx.lineWidth = 8; ctx.stroke();
      ctx.setTransform(dpr,0,0,dpr,0,0);
    },

    // 3) Rotating radial spectrum
    radialSpectrum(){
      const w = canvas.clientWidth, h = canvas.clientHeight; ctx.clearRect(0,0,w,h);
      const cx = w/2, cy = h/2; const r = Math.min(w,h)*0.28; const maxLen = Math.min(w,h)*0.3;
      ctx.save(); ctx.translate(cx,cy); ctx.rotate(Date.now()/5000);
      for(let i=0;i<freqData.length;i++){
        const t = i/freqData.length; const ang = t*TAU;
        const len = (freqData[i]/255) * maxLen;
        ctx.strokeStyle = `hsla(${lerp(170,200,t)}, 90%, 60%, .7)`;
        ctx.beginPath(); ctx.moveTo(Math.cos(ang)*r, Math.sin(ang)*r);
        ctx.lineTo(Math.cos(ang)*(r+len), Math.sin(ang)*(r+len)); ctx.stroke();
      }
      ctx.restore();
    },

    // 4) Dot matrix
    dotMatrix(){
      const w = canvas.clientWidth, h = canvas.clientHeight; ctx.clearRect(0,0,w,h);
      const cols = 64, rows = 36; const cellW = w/cols, cellH = h/rows;
      for(let c=0;c<cols;c++){
        const idx = Math.floor((c/cols)*freqData.length);
        const amp = freqData[idx]/255;
        const dots = Math.floor(amp * rows);
        for(let r=0;r<dots;r++){
          const x = c*cellW + cellW/2; const y = h - (r*cellH + cellH/2);
          ctx.beginPath(); ctx.arc(x,y, Math.max(1.5, cellW*0.18), 0, TAU);
          ctx.fillStyle = `rgba(160,255,255,${lerp(.25,1,amp)})`; ctx.fill();
        }
      }
    },

    // 5) Radial burst spikes
    radialBurst(){
      const w = canvas.clientWidth, h = canvas.clientHeight; ctx.clearRect(0,0,w,h);
      const cx = w/2, cy = h/2; const base = Math.min(w,h)*0.1; const max = Math.min(w,h)*0.45;
      ctx.save(); ctx.translate(cx,cy);
      const rot = Date.now()/2500; ctx.rotate(rot);
      for(let i=0;i<freqData.length;i+=2){
        const t = i/freqData.length; const a = t*TAU;
        const len = base + (freqData[i]/255)*(max-base);
        ctx.strokeStyle = `hsla(${180 + Math.sin(t*6+rot)*20},90%,60%,.8)`;
        ctx.beginPath(); ctx.moveTo(0,0); ctx.lineTo(Math.cos(a)*len, Math.sin(a)*len); ctx.stroke();
      }
      ctx.restore();
    },

    // 6) Oscilloscope line
    oscilloscope(){
      const w = canvas.clientWidth, h = canvas.clientHeight; ctx.clearRect(0,0,w,h);
      ctx.beginPath();
      for(let i=0;i<timeData.length;i++){
        const x = (i/timeData.length) * w;
        const y = h/2 + ( (timeData[i]-128)/128 ) * (h*0.42);
        if(i===0) ctx.moveTo(x,y); else ctx.lineTo(x,y);
      }
      ctx.strokeStyle = '#aefcff'; ctx.lineWidth = 2; ctx.stroke();
      ctx.strokeStyle = 'rgba(0,229,255,.25)'; ctx.lineWidth = 8; ctx.stroke();
    },

    // 7) Geometric polygon pulse
    polyPulse(){
      const w = canvas.clientWidth, h = canvas.clientHeight; ctx.clearRect(0,0,w,h);
      const cx=w/2, cy=h/2; const sides = 6; const baseR = Math.min(w,h)*0.18; const b = bassLevel();
      const R = baseR * (1 + b*0.8);
      ctx.save(); ctx.translate(cx,cy); ctx.rotate(Date.now()/1500);
      ctx.beginPath();
      for(let i=0;i<sides;i++){
        const a = (i/sides)*TAU; const x=Math.cos(a)*R, y=Math.sin(a)*R;
        if(i===0) ctx.moveTo(x,y); else ctx.lineTo(x,y);
      }
      ctx.closePath(); ctx.strokeStyle = '#aefcff'; ctx.lineWidth = 2; ctx.stroke();

      // inner connecting lines using higher mids
      ctx.globalAlpha = .6;
      for(let i=0;i<sides;i++){
        const idx = Math.floor((i/sides)*freqData.length);
        const len = (freqData[idx]/255) * R*0.8;
        const a = (i/sides)*TAU; const x = Math.cos(a)*len, y=Math.sin(a)*len;
        ctx.beginPath(); ctx.moveTo(0,0); ctx.lineTo(x,y); ctx.stroke();
      }
      ctx.globalAlpha = 1; ctx.restore();
    },

    // 8) Particle pulse
    particlePulse(){
      const w = canvas.clientWidth, h = canvas.clientHeight; ctx.fillStyle = 'rgba(6,8,10,.25)'; ctx.fillRect(0,0,w,h);
      const cx=w/2, cy=h/2; const b = bassLevel();
      // spawn more on bass
      const spawn = Math.floor(lerp(0, 12, b));
      for(let i=0;i<spawn;i++){
        const ang = Math.random()*TAU; const sp = lerp(1,6, b+Math.random()*0.3);
        particles.push({x:cx,y:cy,vx:Math.cos(ang)*sp,vy:Math.sin(ang)*sp,life:1, size: lerp(1.5,5,b+Math.random()*0.4)});
      }
      // update/draw
      for(let i=particles.length-1;i>=0;i--){
        const p = particles[i];
        p.x += p.vx; p.y += p.vy; p.life -= 0.01; p.vx *= 0.99; p.vy *= 0.99;
        if(p.life<=0 || p.x<0||p.x>w||p.y<0||p.y>h) { particles.splice(i,1); continue; }
        ctx.beginPath(); ctx.arc(p.x,p.y,p.size,0,TAU);
        ctx.fillStyle = `rgba(174,252,255,${p.life})`; ctx.fill();
      }
    },

    // 9) Grid heatmap
    gridHeatmap(){
      const w = canvas.clientWidth, h = canvas.clientHeight; ctx.clearRect(0,0,w,h);
      const cols = 32, rows = 18; const cw = w/cols, ch = h/rows;
      for(let r=0;r<rows;r++){
        for(let c=0;c<cols;c++){
          const idx = Math.floor(((r*cols+c)/(cols*rows)) * freqData.length);
          const v = freqData[idx]/255; // 0..1
          const x = c*cw, y = r*ch;
          ctx.fillStyle = `rgba(0,229,255,${Math.pow(v,1.3)})`;
          ctx.fillRect(x,y,cw,ch);
        }
      }
      // grid lines
      ctx.strokeStyle = 'rgba(0,40,50,.5)'; ctx.lineWidth = 1;
      for(let c=0;c<=cols;c++){ ctx.beginPath(); ctx.moveTo(c*cw,0); ctx.lineTo(c*cw,h); ctx.stroke(); }
      for(let r=0;r<=rows;r++){ ctx.beginPath(); ctx.moveTo(0,r*ch); ctx.lineTo(w,r*ch); ctx.stroke(); }
    },

    // 10) Spectrum tunnel
    spectrumTunnel(){
      const w = canvas.clientWidth, h = canvas.clientHeight; ctx.fillStyle='rgba(6,8,10,.2)'; ctx.fillRect(0,0,w,h);
      const cx=w/2, cy=h/2; const rings = 18; const maxR = Math.min(w,h)*0.6;
      const time = Date.now()/1200;
      for(let i=0;i<rings;i++){
        const t = (i/rings);
        const r = (t*t) * maxR; // quadratic spacing for depth
        const idx = Math.floor(t * (freqData.length-1));
        const a = freqData[idx]/255;
        const thickness = lerp(1, 10, a);
        ctx.beginPath(); ctx.arc(cx,cy, r + Math.sin(time+t*6)*2, 0, TAU);
        ctx.strokeStyle = `rgba(174,252,255,${lerp(.05,.9,a)})`;
        ctx.lineWidth = thickness; ctx.stroke();
      }
    }
  };

  // Animation loop
  function render(){
    requestAnimationFrame(render);
    if (!analyser) return;
    analyser.getByteFrequencyData(freqData);
    analyser.getByteTimeDomainData(timeData);
    const mode = vizSelect.value;
    visualizers[mode]();
  }
  render();

  // Keep canvas sized to container
  const resizeObserver = new ResizeObserver(sizeCanvas);
  resizeObserver.observe(canvas);
})();
</script>
</body>
</html>
